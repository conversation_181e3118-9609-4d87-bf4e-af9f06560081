# Quick Azure Deployment

## 1. Install Azure CLI
```bash
# Windows
winget install Microsoft.AzureCLI

# Or download from: https://aka.ms/installazurecliwindows
```

## 2. Build Your App
```bash
cd clinical-quality-app
npm run build
```

## 3. Create Azure Static Web App
```bash
# Login to Azure
az login

# Create resource group
az group create --name clinical-quality-rg --location "East US"

# Create static web app
az staticwebapp create \
  --name clinical-quality-ui \
  --resource-group clinical-quality-rg \
  --location "East US2" \
  --source dist/clinical-quality-app
```

## 4. Configure SPA Routing
Create `staticwebapp.config.json` in your build output:
```json
{
  "navigationFallback": {
    "rewrite": "/index.html",
    "exclude": ["/assets/*", "/*.{css,scss,js,png,ico,svg}"]
  }
}
```

Your app will be available at a URL like:
`https://clinical-quality-ui-<random>.azurestaticapps.net`
