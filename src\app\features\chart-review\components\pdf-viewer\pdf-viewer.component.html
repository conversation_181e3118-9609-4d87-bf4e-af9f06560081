<div class="pdf-viewer-container">
  <!-- PDF Viewer -->
  <div class="pdf-viewer-wrapper">
    @if (isBrowser && pdfBase64Source) {
      <ngx-extended-pdf-viewer
        [src]="pdfBase64Source"
        [(page)]="currentPage"
        [zoom]="zoom"
        [autoScale]="'page-fit'"
        [defaultZoom]="'page-fit'"
        (zoomChange)="onZoomChange($event)"
        (pdfLoaded)="onPdfLoaded($event)"
        (pageChange)="onPageChange($event)"
        [height]="viewerHeight"
        [language]="'en-US'"
        [showToolbar]="true"
        [showSidebarButton]="true"
        [showFindButton]="true"
        [showPagingButtons]="true"
        [showZoomButtons]="true"
        [showPresentationModeButton]="true"
        [showOpenFileButton]="true"
        [showPrintButton]="true"
        [showDownloadButton]="true"
        [showBookmodeButton]="'always-in-secondary-menu'"
        [showSecondaryToolbarButton]="true"
        [showRotateButton]="true"
        [showHandToolButton]="true"
        [showScrollingButtons]="'always-in-secondary-menu'"
        [showSpreadButton]="true"
        [showPropertiesButton]="true"
        [scrollMode]="scrollMode"
        [disableForms]="!renderInteractiveForms"
        [minZoom]="minZoom"
        [maxZoom]="maxZoom"
        (error)="onPdfViewerError($event)"
      ></ngx-extended-pdf-viewer>
    }

    <!-- Message when no PDF is loaded -->
    @if (isBrowser && !pdfBase64Source) {
      <div class="no-pdf-message">
        <p>Please select a PDF file to view.</p>
      </div>
    }

    <!-- Message when not in browser -->
    @if (!isBrowser) {
      <div class="ssr-placeholder">
        PDF Viewer is not available during server-side rendering.
      </div>
    }
  </div>
</div>
