<div class="exclusions-tab-content" [formGroup]="formGroup">

  <!-- Form Fields Section - exact Figma frame-916 -->
  <div class="frame-916">

    <!-- Reasoning Dropdown - exact Figma frame-939 -->
    <div class="frame-939">
      <span class="text-reasoning">Reasoning</span>
      <app-dropdown
        placeholder="Select"
        [options]="exclusionsReasoningOptions"
        [multiSelect]="true"
        formControlName="reasoning"
        [disabled]="disabled"
        class="dropdown-exclusion"
        (selectionChange)="onFormFieldChange()">
      </app-dropdown>
    </div>

    <!-- Date of Service - exact Figma frame-940 -->
    <div class="frame-940">
      <span class="text-date-of-service">Date of Service</span>
      <app-calendar
        placeholder="MM/DD/YY"
        formControlName="dateOfService"
        [disabled]="disabled"
        class="calendar"
        (dateChange)="onFormFieldChange()">
      </app-calendar>
    </div>

    <!-- Notes Field - exact Figma frame-941 -->
    <div class="frame-941">
      <span class="text-notes">Notes</span>
      <app-notes
        label=""
        placeholder="Notes"
        formControlName="notes"
        [disabled]="disabled"
        class="notes"
        (notesChange)="onFormFieldChange()">
      </app-notes>
    </div>

  </div>
</div>
