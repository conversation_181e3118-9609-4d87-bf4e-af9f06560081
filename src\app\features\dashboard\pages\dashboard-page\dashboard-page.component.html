<app-menu
  logoSrc="assets/logos/Stellarus_logo_2C_blacktype.png?v=2024"
  logoAlt="Stellarus Logo"
  [user]="userProfile"
  [menuItems]="menuItems"
  (logoClick)="onLogoClick()"
  (userClick)="onUserClick()"
  (dropdownToggle)="onDropdownToggle($event)"
  (menuItemClick)="onMenuItemClick($event)">
</app-menu>

<!-- Page Title -->
<app-page-title [title]="'Dashboard'"></app-page-title>

<!-- Instance ID: {{instanceId}} -->
<div class="dashboard-container">
  <!-- Assigned Charts Section -->
  <div class="dashboard-section">
    <div class="section-header">
      <h2>Assigned charts</h2>
      <div class="section-actions">
        <app-button
          variant="secondary"
          (buttonClick)="refreshCharts()"
          #refreshButton>
          <app-refresh-icon [color]="refreshButton.getIconColor()"></app-refresh-icon>Refresh charts
        </app-button>
      </div>
    </div>

    <app-assigned-table [charts]="assignedCharts" [searchText]="assignedSearchText"></app-assigned-table>
  </div>
</div>
