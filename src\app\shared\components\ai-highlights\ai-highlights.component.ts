import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';

import { FormsModule } from '@angular/forms';
import { CheckboxComponent } from '../form-controls/checkbox/checkbox.component';

export interface AiHighlightData {
  id: string;
  measure: string;
  dateOfService: string;
  systolic: number;
  diastolic: number;
  page: number;
  include: boolean;
}

@Component({
  selector: 'app-ai-highlights',
  standalone: true,
  imports: [FormsModule, CheckboxComponent],
  templateUrl: './ai-highlights.component.html',
  styleUrls: ['./ai-highlights.component.scss']
})
export class AiHighlightsComponent implements OnInit {
  @Input() title: string = 'AI Highlights';
  @Input() data: AiHighlightData[] = [];
  @Input() showHeader: boolean = true;
  
  // Track the currently selected highlight (the one user clicked to navigate to)
  selectedHighlightId: string | null = null;

  @Output() dataChange = new EventEmitter<AiHighlightData[]>();
  @Output() pageClick = new EventEmitter<{ highlight: AiHighlightData, page: number }>();
  @Output() includeChange = new EventEmitter<{ highlight: AiHighlightData, include: boolean }>();

  ngOnInit(): void {
    console.log('[AiHighlights] *** AI HIGHLIGHTS COMPONENT INITIALIZED ***');
    console.log('[AiHighlights] Component title:', this.title);
    console.log('[AiHighlights] Component data:', this.data);

    if (this.data.length === 0) {
      this.data = this.getDefaultData();
      console.log('[AiHighlights] Using default data:', this.data);
    }

    // No highlight should be selected by default
    this.selectedHighlightId = null;
    console.log('[AiHighlights] Component initialization complete');
  }

  onIncludeChange(highlight: AiHighlightData, include: boolean): void {
    // Update the 'include' property of the selected highlight
    highlight.include = include;

    // Update the selected highlight ID if the checkbox is checked
    if (include) {
      this.selectedHighlightId = highlight.id;
    }

    // Emit events to notify parent components
    this.includeChange.emit({ highlight, include });
    this.dataChange.emit(this.data);

    // Emit the row's data when 'include' is checked
    if (highlight.include) { // Ensure the include property is true
      console.log('[AiHighlights] Emitting data for inclusion:', highlight);
      this.pageClick.emit({ highlight, page: highlight.page });
    }
  }

  onPageClick(highlight: AiHighlightData): void {
    // Log the page click event for debugging purposes
    console.log('[AiHighlights] Page click detected!');
    console.log('[AiHighlights] Emitting highlight data:', highlight);

    // Update the selected highlight ID
    this.selectedHighlightId = highlight.id;

    // Emit an event to notify parent components about the page click
    this.pageClick.emit({ highlight, page: highlight.page });
  }

  trackByHighlightId(_index: number, highlight: AiHighlightData): string {
    return highlight.id;
  }

  private getDefaultData(): AiHighlightData[] {
    return [
      {
        id: 'highlight-1',
        measure: 'CBP',
        dateOfService: '07/21/24',
        systolic: 136,
        diastolic: 82,
        page: 2,
        include: false
      },
      {
        id: 'highlight-2',
        measure: 'CBP',
        dateOfService: '07/21/24',
        systolic: 140,
        diastolic: 82,
        page: 2,
        include: false
      },
      {
        id: 'highlight-3',
        measure: 'CBP',
        dateOfService: '05/21/24',
        systolic: 150,
        diastolic: 90,
        page: 7,
        include: false
      }
    ];
  }
}
