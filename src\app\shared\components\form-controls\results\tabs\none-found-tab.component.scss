@use 'variables' as variables;

// Exact Figma specifications for none-found tab
.none-found-tab-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: stretch; // Changed to stretch to ensure full width
  gap: 12px;
  box-sizing: border-box;
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  font-family: Urbane; // Apply Urbane font
}

// Form fields container - exact Figma frame-916
.frame-916 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: stretch; // Changed to stretch to ensure full width
  gap: 12px; // Exact Figma gap
  box-sizing: border-box;
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
}

// Reasoning section - exact Figma frame-942
.frame-942 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: stretch; // Changed to stretch to ensure full width
  gap: 4px; // Exact Figma gap
  box-sizing: border-box;
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
}

// Notes section - exact Figma frame-943
.frame-943 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: stretch; // Changed to stretch to ensure full width
  gap: 4px; // Exact Figma gap
  box-sizing: border-box;
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
}

// Label styling - exact Figma specifications
.text-reasoning,
.text-notes {
  color: variables.$gray-3; // Exact Figma color
  font-size: 10px; // Exact Figma font size
  font-family: 'Urbane', sans-serif; // Exact Figma font family
  line-height: 20px; // Exact Figma line height
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300; // Exact Figma font weight
  text-align: left;
  text-wrap: nowrap;
}

// Dropdown styling - exact Figma dropdown-none specifications
.dropdown-none {
  height: 44px; // Exact Figma height
  width: 100% !important; // Force full width
  max-width: none !important; // Remove any max-width constraints
  min-width: 0 !important; // Allow full expansion
  flex: 1 1 100% !important; // Force flex to take full width
  box-sizing: border-box;
}

// Notes component styling - exact Figma specifications
.notes {
  width: 100% !important; // Force full width
  max-width: none !important; // Remove any max-width constraints
  min-width: 0 !important; // Allow full expansion
  flex: 1 1 100% !important; // Force flex to take full width
  box-sizing: border-box;
}

// Responsive design
@media (max-width: 768px) {
  .frame-916 {
    gap: 8px;
  }
}
