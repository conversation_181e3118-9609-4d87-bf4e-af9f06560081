@use 'variables' as variables;
@use 'mixins' as mix;

.results-container {
  padding: 20px; // Outer container padding
  display: flex; // Exact Figma display from results.scss line 5
  flex-direction: column; // Exact Figma flex-direction from results.scss line 6
  justify-content: center; // Exact Figma justify from results.scss line 7
  align-items: stretch; // Changed to stretch for full width
  gap: 0px; // Exact Figma gap from results.scss line 10
  border-radius: 8px; // Exact Figma border-radius from results.scss line 12
  border: 1px solid #f1f5f7; // Exact Figma border from results.scss lines 13-15
  background: #ffffff; // Exact Figma background from results.scss line 16
  width: 100%; // Exact Figma width from results.scss line 17
  box-sizing: border-box;

  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  &.has-error {
    border-color: #F4454E;
  }

  .results-header {
    padding: 12px; // Header section padding
  }

  .tab-navigation {
    padding: 8px 8px 0 8px; // Removed bottom padding to eliminate gap
    display: flex; // Exact Figma display from results.scss line 82
    flex-direction: row; // Exact Figma flex-direction from results.scss line 83
    justify-content: flex-start; // Exact Figma justify from results.scss line 84
    align-items: center; // Exact Figma align from results.scss line 86
    gap: 16px;
    border-bottom: 1px solid #d9e1e7; // Exact Figma border from results.scss lines 89-91
    background: #ffffff; // Exact Figma background from results.scss line 92
    width: 100%; // Exact Figma width from results.scss line 93
    box-sizing: content-box; // Exact Figma box-sizing from results.scss line 88
    position: relative; // Added for positioning the blue underline
  }

  .tab-button {
    padding: 8px 8px 16px 8px; // Increased bottom padding to extend to the gray line
    display: flex; // Exact Figma display from results.scss line 140
    flex-direction: row; // Exact Figma flex-direction from results.scss line 141
    justify-content: flex-start; // Exact Figma justify from results.scss line 142
    align-items: center; // Exact Figma align from results.scss line 144
    gap: 12px; // Exact Figma gap from results.scss line 145
    height: 100%; // Exact Figma height from results.scss line 147
    background: transparent;
    border: none;
    border-bottom: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    box-sizing: border-box;
    position: relative; // Added for positioning the blue underline
    margin-bottom: 0; // Reset margin

    // Tab button text styling
    color: #547996; // Exact Figma color from results.scss line 199 (gray-3)
    font-weight: 600; // Exact Figma font-weight from results.scss line 162
    text-align: left; // Exact Figma text-align from results.scss line 163
    text-wrap: nowrap; // Exact Figma text-wrap from results.scss line 164

    &:hover:not(:disabled) {
      color: #17181a; // text-black
    }

    &.active {
      color: #1976d2; // primary-blue from results.scss line 160
      position: relative;
      border-bottom: none; // Remove the default border
      z-index: 1; // Ensures the blue indicator appears on top of the gray border
      
      &::after {
        content: '';
        position: absolute;
        bottom: -1px; // Position it to overlap the gray border
        left: 0;
        width: 100%; // Full width of the tab text
        height: 2px; // Thickness of the blue indicator
        background-color: #1976d2; // Blue color for the indicator
        z-index: 2; // Ensure it's above the gray border
      }
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }

  .results-title-section {
    display: flex; // Exact Figma display from results.scss line 31
    flex-direction: column; // Exact Figma flex-direction from results.scss line 32
    justify-content: flex-start; // Exact Figma justify from results.scss line 33
    align-items: flex-start; // Exact Figma align from results.scss line 35
    gap: 12px; // Exact Figma gap from results.scss line 36
    width: 100%; // Exact Figma width from results.scss line 38
    box-sizing: border-box;
  }

  .results-title {
    color: #17181A;
    font-family: Urbane;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 32px; /* 160% */
    text-align: left; // Exact Figma text-align from results.scss line 54
    text-wrap: nowrap; // Exact Figma text-wrap from results.scss line 55
    margin: 0;
  }

  .results-content {
    padding: 12px; // Telehealth section padding
  }

  .date-of-service-section {
    padding: 4px; // Date of Service section padding
  }

  .notes-section {
    padding: 4px; // Notes section padding
  }
}

.error-message {
  color: #F4454E;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  line-height: 16px;
  margin-top: 4px;
}

// Responsive design
@media (max-width: 768px) {
  .results-container {
    padding: 16px;
  }

  .tab-navigation {
    flex-wrap: wrap;
    gap: 8px;
  }

  .tab-button {
    font-size: 12px;
    padding: 2px 6px;
  }
}