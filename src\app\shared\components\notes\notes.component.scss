@use 'variables' as variables;
@use 'mixins' as mix;

.notes-container {
  position: relative;
  width: 100%;

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.notes-label {
  display: block;
  font-size: 10px; // Figma specification
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: variables.$gray-3; // Figma specification
  margin-bottom: 4px; // Figma spacing

  .required-indicator {
    color: variables.$primary-blue;
    margin-left: 2px;
  }
}

// Simple Notes Input Wrapper (Figma design)
.notes-input-wrapper {
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

// Simple Notes Textarea (Figma design)
.notes-textarea {
  width: 100%;
  min-height: 120px; // Large height as shown in Figma
  padding: 12px;
  border: 1px solid variables.$gray-2;
  border-radius: 8px;
  background: #ffffff;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  line-height: 16px;
  color: variables.$gray-3;
  resize: vertical; // Allow vertical resize
  box-sizing: border-box;
  outline: none;
  transition: all 0.2s ease;

  &::placeholder {
    color: variables.$gray-3;
  }

  &:focus {
    border-color: variables.$gray-3;
    color: variables.$text-black;
  }

  &.has-value {
    color: variables.$text-black;
  }

  &:disabled {
    background-color: variables.$gray-1;
    cursor: not-allowed;
    color: variables.$gray-3;
  }

  &:hover:not(:disabled) {
    border-color: variables.$gray-3;
  }

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: variables.$gray-2;
    border-radius: 3px;

    &:hover {
      background: variables.$gray-3;
    }
  }
}

// Character Counter (bottom-right overlay)
.character-counter {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 10px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  line-height: 20px;
  color: variables.$gray-3;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 4px;
  pointer-events: none;

  &.near-limit {
    color: #F4A261; // Warning color
  }

  &.over-limit {
    color: #F4454E; // Error color
  }
}

.error-message {
  margin-top: variables.$spacing-xs;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: #F4454E;
  line-height: 16px;
}

// Responsive design
@include mix.for-phone-only {
  .notes-textarea {
    min-height: 100px; // Slightly smaller for mobile
    padding: 10px;
  }

  .character-counter {
    font-size: 9px;
    bottom: 6px;
    right: 10px;
  }
}

@include mix.for-tablet-portrait-up {
  .notes-textarea {
    min-height: 120px; // Full height for larger screens
    padding: 12px;
  }

  .character-counter {
    font-size: 10px;
    bottom: 8px;
    right: 12px;
  }

}