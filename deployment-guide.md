# Clinical Quality UI Deployment Guide

## Recommended Deployment: Static Web Hosting

### Build the Application
```bash
cd clinical-quality-app
npm run build
```

### Deploy to Azure Static Web Apps
```bash
# Install Azure CLI if not already installed
npm install -g @azure/static-web-apps-cli

# Deploy to Azure
swa deploy ./dist/clinical-quality-app --deployment-token <your-token>
```

### Deploy to AWS S3 + CloudFront
```bash
# Build the app
npm run build

# Upload to S3 bucket
aws s3 sync ./dist/clinical-quality-app s3://your-bucket-name

# Configure CloudFront for SPA routing
```

### Deploy to Netlify
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Build and deploy
npm run build
netlify deploy --prod --dir=dist/clinical-quality-app
```

## Alternative: Containerized Deployment

### Docker Deployment
```dockerfile
# Dockerfile
FROM node:18-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist/clinical-quality-app /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

Deploy to:
- Azure Container Instances
- AWS ECS
- Kubernetes cluster
