import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit, Inject, PLATFORM_ID, NO_ERRORS_SCHEMA } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { NgxExtendedPdfViewerModule, NgxExtendedPdfViewerService, pdfDefaultOptions } from 'ngx-extended-pdf-viewer';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-pdf-viewer-test',
  standalone: true,
  imports: [NgxExtendedPdfViewerModule, FormsModule],
  templateUrl: './pdf-viewer-test.component.html',
  styleUrl: './pdf-viewer-test.component.scss',
  schemas: [NO_ERRORS_SCHEMA] // Add this to suppress property binding errors
})
export class PdfViewerTestComponent implements OnInit, AfterViewInit, OnDestroy {
  // PDF viewer state
  currentPage: number = 1;
  totalPages: number = 0;
  zoom: number = 100.0; // IMPORTANT: This value must remain high to ensure proper initial zoom.
                        // The PDF viewer's internal zoom is scaled differently than expected.
                        // Setting this to 1.0 results in the PDF loading at only 1% zoom.
  
  // File state
  currentFileName: string = '';
  
  private subscriptions: Subscription = new Subscription();
  public isBrowser: boolean = false; // Initialize with a default value
  public pdfBase64Source: string | null = null; // Store the base64 source
  
  // Configuration options
  public enableRangeRequests: boolean = true;
  public scrollMode: number = 1; // 0 = vertical, 1 = horizontal, 2 = wrapped
  public spreadMode: number = 0; // 0 = none, 1 = odd, 2 = even
  public fitToPage: boolean = true;
  public minZoom: number = 0.5; // Already in decimal format (50%)
  public maxZoom: number = 2.0; // Already in decimal format (200%)
  public renderTextMode: number = 2; // 0 = disabled, 1 = enabled, 2 = enhanced
  public renderInteractiveForms: boolean = true;
  public viewerPositionTop: number = 0;
  public viewerHeight: string = '100%';

  constructor(
    private pdfViewerService: NgxExtendedPdfViewerService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    
    // Configure global PDF.js options
    if (this.isBrowser) {
      // Note: enableRangeRequests is not directly accessible in pdfDefaultOptions
      // We'll use the component property and pass it to the viewer instead
      pdfDefaultOptions.disableStream = false;
      pdfDefaultOptions.disableAutoFetch = false;
    }
  }

  ngOnInit(): void {
    console.log('[PdfViewerTest] ngOnInit called');
    console.log('[PdfViewerTest] Browser environment:', this.isBrowser ? 'Browser' : 'Server');
    console.log('[PdfViewerTest] Initial zoom value:', this.zoom, '(decimal format)');
    console.log('[PdfViewerTest] Range requests enabled:', this.enableRangeRequests);
    console.log('[PdfViewerTest] Scroll mode:', this.getScrollModeName(this.scrollMode));
    console.log('[PdfViewerTest] Spread mode:', this.getSpreadModeName(this.spreadMode));
    console.log('[PdfViewerTest] Spread value:', this.getSpreadValue());
    console.log('[PdfViewerTest] Fit to page:', this.fitToPage);
    console.log('[PdfViewerTest] Forms enabled:', this.renderInteractiveForms);
    
    // Check if PDF.js worker is configured
    if (this.isBrowser) {
      console.log('[PdfViewerTest] PDF worker configuration:', (window as any).pdfWorkerSrc);
    }
    
    // Log the version of ngx-extended-pdf-viewer being used
    console.log('[PdfViewerTest] Using ngx-extended-pdf-viewer version 23.1.1');
  }
  
  ngAfterViewInit(): void {
    console.log('[PdfViewerTest] ngAfterViewInit called');
    console.log('[PdfViewerTest] PDF viewer ready for rendering');
    
    // Add a small delay to check if the viewer is properly initialized
    setTimeout(() => {
      console.log('[PdfViewerTest] Delayed check - Zoom value:', this.zoom, '(decimal format)');
      console.log('[PdfViewerTest] Viewer container dimensions:', this.getViewerContainerDimensions());
    }, 1000);
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Handles file selection from the file input
   * @param event The file input change event
   */
  onFileSelected(event: Event): void {
    // Ensure this only runs in the browser
    if (!this.isBrowser) {
      console.error('[PdfViewerTest] PDF loading attempted during server-side rendering');
      return;
    }

    console.log('[PdfViewerTest] File selection event triggered');
    
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      console.log(`[PdfViewerTest] File selected: ${file.name}, type: ${file.type}, size: ${file.size} bytes`);
      
      if (file.type === 'application/pdf') {
        this.currentFileName = file.name;
        this.pdfBase64Source = null; // Reset while loading
        console.log('[PdfViewerTest] Valid PDF file selected, beginning conversion to base64');

        // Read file as base64
        const reader = new FileReader();
        
        reader.onloadstart = () => {
          console.log('[PdfViewerTest] FileReader: Started loading file');
        };
        
        reader.onprogress = (event) => {
          if (event.lengthComputable) {
            const percentLoaded = Math.round((event.loaded / event.total) * 100);
            console.log(`[PdfViewerTest] FileReader: Loading progress: ${percentLoaded}%`);
          }
        };
        
        reader.onload = (e) => {
          console.log('[PdfViewerTest] FileReader: File successfully loaded');
          const rawResult = e.target?.result as string;
          
          this.pdfBase64Source = rawResult;
          
          // Log the first 100 chars and length of the assigned base64 string
          const base64Preview = this.pdfBase64Source?.substring(0, 100) + '...';
          console.log(`[PdfViewerTest] Assigned pdfBase64Source length: ${this.pdfBase64Source?.length ?? 'null'}`);
          console.log(`[PdfViewerTest] Assigned pdfBase64Source (preview): ${base64Preview}`);
          
          // Check if it starts with the correct data URL prefix for PDFs
          if (this.pdfBase64Source?.startsWith('data:application/pdf;base64,')) {
            console.log('[PdfViewerTest] Base64 data has correct PDF MIME type prefix');
          } else {
            console.warn('[PdfViewerTest] Base64 data does not have expected PDF MIME type prefix');
          }
          
          console.log('[PdfViewerTest] PDF base64 source set, viewer should update');
        };
        
        reader.onerror = (error) => {
          console.error('[PdfViewerTest] FileReader error:', error);
          this.pdfBase64Source = null;
          this.currentFileName = '';
          this.totalPages = 0;
          alert('Error reading the selected PDF file.');
        };
        
        console.log('[PdfViewerTest] Starting FileReader.readAsDataURL...');
        reader.readAsDataURL(file);
      } else {
        this.pdfBase64Source = null;
        this.currentFileName = '';
        this.totalPages = 0;
        console.error(`[PdfViewerTest] Invalid file type: ${file.type}, expected application/pdf`);
        alert('Please select a valid PDF file.');
      }
    } else {
      console.warn('[PdfViewerTest] No file selected or file input event without files');
      this.pdfBase64Source = null;
      this.currentFileName = '';
      this.totalPages = 0;
    }
  }

  /**
   * Handles PDF loaded event
   * @param pdf The loaded PDF document
   */
  onPdfLoaded(pdf: any): void {
    console.log('[PdfViewerTest] PDF loaded event triggered');
    console.log('[PdfViewerTest] PDF document:', pdf);
    
    if (pdf && pdf.numPages) {
      this.totalPages = pdf.numPages;
      console.log(`[PdfViewerTest] PDF loaded with ${this.totalPages} pages`);
    }
    
    // Log viewer dimensions after PDF is loaded
    setTimeout(() => {
      console.log('[PdfViewerTest] Viewer dimensions after PDF load:', this.getViewerContainerDimensions());
      console.log('[PdfViewerTest] Current zoom after PDF load:', this.zoom, '(decimal format)');
    }, 500);
  }

  /**
   * Handles page change events
   * @param pageNumber The new page number
   */
  onPageChange(pageNumber: number): void {
    console.log(`[PdfViewerTest] Page changed to ${pageNumber}`);
    this.currentPage = pageNumber;
  }

  /**
   * Handles zoom change events from the PDF viewer
   * @param newZoom New zoom level (as a decimal, e.g., 1.0 for 100%)
   */
  onZoomChange(newZoom: string | number): void {
    // We still need to update our internal zoom property for the [zoom] binding
    // but we no longer display it in the UI or provide external controls
    const zoomValue = typeof newZoom === 'string' ? parseFloat(newZoom) : newZoom;
    console.log(`[PdfViewerTest] PDF viewer zoom changed to ${zoomValue}`);
    
    // Store the zoom value without multiplying by 100
    // This prevents the scaling issue where 1.9 became 190%
    this.zoom = zoomValue;
  }

  // Removed zoomIn and zoomOut methods as they're no longer needed
  // The PDF viewer's built-in zoom controls will be used instead

  /**
   * Toggles range requests
   * Note: In v23.1.1, enableRangeRequests is no longer directly supported
   * This method is kept for UI consistency but doesn't affect the PDF viewer
   */
  toggleRangeRequests(): void {
    this.enableRangeRequests = !this.enableRangeRequests;
    console.log(`[PdfViewerTest] Range requests ${this.enableRangeRequests ? 'enabled' : 'disabled'} (UI only, not supported in v23.1.1)`);
    console.log('[PdfViewerTest] Note: enableRangeRequests is no longer directly supported in ngx-extended-pdf-viewer v23.1.1');
  }

  /**
   * Toggles fit to page
   * Note: In v23.1.1, fitToPage is no longer directly supported
   * This method is kept for UI consistency but doesn't affect the PDF viewer
   */
  toggleFitToPage(): void {
    this.fitToPage = !this.fitToPage;
    console.log(`[PdfViewerTest] Fit to page ${this.fitToPage ? 'enabled' : 'disabled'} (UI only, not supported in v23.1.1)`);
    console.log('[PdfViewerTest] Note: fitToPage is no longer directly supported in ngx-extended-pdf-viewer v23.1.1');
  }

  /**
   * Changes the scroll mode
   */
  changeScrollMode(mode: number): void {
    this.scrollMode = mode;
    console.log(`[PdfViewerTest] Scroll mode changed to ${this.getScrollModeName(mode)}`);
  }

  /**
   * Changes the spread mode
   */
  changeSpreadMode(mode: number): void {
    this.spreadMode = mode;
    console.log(`[PdfViewerTest] Spread mode changed to ${this.getSpreadModeName(mode)} (${this.getSpreadValue()})`);
  }
  
  /**
   * Gets the spread value for the PDF viewer based on the spreadMode number
   * @returns The spread value as a string ('off', 'odd', or 'even')
   */
  getSpreadValue(): string {
    switch (this.spreadMode) {
      case 0: return 'off';
      case 1: return 'odd';
      case 2: return 'even';
      default: return 'off';
    }
  }

  /**
   * Handles PDF viewer errors
   * @param error The error object from the PDF viewer
   */
  onPdfViewerError(error: any): void {
    console.error('[PdfViewerTest] PDF Viewer Error:', error);
    // Log additional context that might help diagnose the issue
    console.error('[PdfViewerTest] PDF Viewer Context:', {
      hasBase64Source: !!this.pdfBase64Source,
      base64SourceLength: this.pdfBase64Source ? this.pdfBase64Source.length : 0,
      base64SourcePrefix: this.pdfBase64Source ? this.pdfBase64Source.substring(0, 50) : 'N/A',
      fileName: this.currentFileName,
      totalPages: this.totalPages,
      workerSrc: this.isBrowser ? (window as any).pdfWorkerSrc : 'N/A',
      viewerDimensions: this.getViewerContainerDimensions(),
      spreadMode: this.spreadMode,
      spreadValue: this.getSpreadValue(),
      scrollMode: this.scrollMode,
      scrollModeName: this.getScrollModeName(this.scrollMode),
      renderInteractiveForms: this.renderInteractiveForms,
      ngxExtendedPdfViewerVersion: '23.1.1'
    });
  }

  /**
   * Gets the name of the scroll mode
   * @param mode The scroll mode number
   * @returns The name of the scroll mode
   */
  private getScrollModeName(mode: number): string {
    switch (mode) {
      case 0: return 'Vertical';
      case 1: return 'Horizontal';
      case 2: return 'Wrapped';
      default: return 'Unknown';
    }
  }

  /**
   * Gets the name of the spread mode
   * @param mode The spread mode number
   * @returns The name of the spread mode
   */
  private getSpreadModeName(mode: number): string {
    switch (mode) {
      case 0: return 'None';
      case 1: return 'Odd';
      case 2: return 'Even';
      default: return 'Unknown';
    }
  }

  /**
   * Gets the dimensions of the viewer container
   * @returns An object with the width and height of the viewer container
   */
  private getViewerContainerDimensions(): { width: string, height: string } {
    if (!this.isBrowser) {
      return { width: 'N/A', height: 'N/A' };
    }

    const container = document.querySelector('.pdf-test-container') as HTMLElement;
    if (!container) {
      return { width: 'Container not found', height: 'Container not found' };
    }

    return {
      width: `${container.clientWidth}px`,
      height: `${container.clientHeight}px`
    };
  }
}