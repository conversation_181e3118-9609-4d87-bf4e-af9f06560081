<div class="inclusions-tab-content" [formGroup]="formGroup">

  <!-- Telehealth Checkbox -->
  <div class="frame-942">
    <app-checkbox
      label="Telehealth"
      formControlName="telehealth"
      (change)="onFormFieldChange('telehealth')">
    </app-checkbox>
  </div>

  <!-- Form Fields Section -->
  <div class="frame-936">

    <!-- Systolic, Dias, and Date of Service in same row -->
    <div class="frame-939">
      <div class="frame-940">
        <span class="text-systolic">Systolic</span>
        <app-value-input
          formControlName="sys"
          (valueChange)="onFormFieldChange('sys')">
        </app-value-input>
      </div>

      <div class="frame-941">
        <span class="text-diastolic">Diastolic</span>
        <app-value-input
          formControlName="dias"
          (valueChange)="onFormFieldChange('dias')">
        </app-value-input>
      </div>

      <div class="frame-937">
        <span class="text-date-of-service">Date of Service</span>
        <app-calendar
          placeholder="MM/DD/YY"
          formControlName="dateOfService"
          class="calendar"
          (dateChange)="onFormFieldChange('dateOfService')">
        </app-calendar>
      </div>
    </div>

  </div>

  <!-- Notes Field (separate section) -->
  <div class="frame-938">
    <span class="text-notes">Notes</span>
    <app-notes
      label=""
      placeholder="Notes"
      formControlName="notes"
      class="notes"
      (notesChange)="onFormFieldChange('notes')">
    </app-notes>
  </div>

</div>
