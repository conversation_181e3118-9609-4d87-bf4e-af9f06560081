.pdf-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  // Removed background and padding to eliminate extra styling
}

.test-controls {
  background-color: #ffffff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

h2 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 20px;
  color: #333;
}

.file-input-container {
  margin-bottom: 16px;
}

.file-input-label {
  display: inline-block;
  padding: 8px 16px;
  background-color: #2196f3;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #1976d2;
  }
}

.file-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.config-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
  
  label {
    font-size: 14px;
    color: #333;
  }
  
  button, select {
    padding: 4px 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #fff;
    color: #17181A; // Added text color to make buttons readable
    font-size: 14px;
    cursor: pointer;
    
    &:hover {
      background-color: #f0f0f0;
    }
  }
}

.debug-info {
  background-color: #f0f0f0;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  font-family: monospace;
  
  h3 {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  div {
    margin-bottom: 4px;
  }
}

.pdf-viewer-wrapper {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 500px;
  // Removed background, border-radius, and box-shadow to eliminate box styling
}

/* Ensure the ngx-extended-pdf-viewer fills the available space */
:host ::ng-deep ngx-extended-pdf-viewer {
  display: block;
  width: 100%;
  height: 100%;
}

/* Fix for PDF.js viewer container */
:host ::ng-deep .viewer-container {
  overflow: auto !important;
}

/* Fix for PDF.js page container */
:host ::ng-deep .page {
  margin: 8px auto !important;
}

/* Custom styles for search highlighting (Ctrl+F functionality) */
:host ::ng-deep .textLayer .highlight {
  background-color: rgba(180, 235, 180, 0.4) !important; /* Light green with 40% opacity */
  border-radius: 2px;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
  mix-blend-mode: multiply; /* This helps text remain readable */
}

/* Ensure highlighted text remains readable */
:host ::ng-deep .textLayer .highlight.selected {
  background-color: rgba(180, 235, 180, 0.6) !important; /* Slightly more opaque for selected match */
}

.no-pdf-message, .ssr-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 16px;
  color: #666;
  text-align: center;
  padding: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .config-controls {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .pdf-viewer-wrapper {
    height: calc(100vh - 400px);
  }
}