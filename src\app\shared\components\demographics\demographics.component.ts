import { Component, Input, Output, EventEmitter } from '@angular/core';


export interface DemographicsData {
  memberId: string;
  memberName: string;
  dateOfBirth: string;
  gender: string;
  lob: string;
  providerName: string;
  npi: string;
}

@Component({
  selector: 'app-demographics',
  standalone: true,
  imports: [],
  templateUrl: './demographics.component.html',
  styleUrls: ['./demographics.component.scss']
})
export class DemographicsComponent {
  @Input() data: DemographicsData | null = null;
  @Input() showBackButton: boolean = true;
  @Input() backButtonText: string = 'Back';

  @Output() backClick = new EventEmitter<void>();

  onBackClick(): void {
    this.backClick.emit();
  }

  get defaultData(): DemographicsData {
    return {
      memberId: '55820474',
      memberName: 'John <PERSON>',
      dateOfBirth: '01/05/1972',
      gender: 'M',
      lob: 'MAHMO',
      providerName: '<PERSON>',
      npi: '882716229'
    };
  }

  get displayData(): DemographicsData {
    return this.data || this.defaultData;
  }
}
