@use 'variables' as variables;

// Exact Figma specifications for inclusions tab
.inclusions-tab-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 12px;
  box-sizing: border-box;
  width: 100%;
  font-family: Urbane; // Apply Urbane font
}

// Telehealth section - exact Figma frame-942
.frame-942 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px; // Exact Figma gap
  box-sizing: border-box;
}

// Form fields container - exact Figma frame-936
.frame-936 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px; // Exact Figma gap
  box-sizing: border-box;
  width: 100%;
}

// Row containing Sys, Dias, Date of Service - exact Figma frame-939
.frame-939 {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px; // Exact Figma gap
  box-sizing: border-box;
  width: 100%;
}

// systolicfield container - exact Figma frame-940
.frame-940 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px; // Exact Figma gap
  box-sizing: border-box;
  width: 127px; // Exact Figma width
  min-height: 72px; // Ensure visibility for the new component
}

// Dias field container - exact Figma frame-941
.frame-941 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px; // Exact Figma gap
  box-sizing: border-box;
  width: 127px; // Exact Figma width
  min-height: 72px; // Ensure visibility for the new component
}

// Date of Service field container - exact Figma frame-937
.frame-937 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px; // Exact Figma gap
  box-sizing: border-box;
  width: 215px; // Exact Figma width
}

// Notes section - exact Figma frame-938
.frame-938 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: flex-start;
  gap: 4px; // Exact Figma gap
  box-sizing: border-box;
  width: 100%;
}

// Label styling - exact Figma specifications
.text-sys,
.text-dias,
.text-date-of-service,
.text-notes,
.text-systolic,
.text-diastolic {
  color: variables.$gray-3; // Exact Figma color
  font-size: 10px; // Exact Figma font size
  font-family: 'Urbane', sans-serif; // Exact Figma font family
  line-height: 20px; // Exact Figma line height
  letter-spacing: 0%;
  text-decoration: none;
  font-weight: 300; // Exact Figma font weight
  text-align: left;
  text-wrap: nowrap;
}

// Input styling - exact Figma dropdown-inclusion specifications
.dropdown-inclusion {
  padding: 12px; // Based on row padding 8px + 4px
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex-wrap: nowrap;
  align-items: center;
  gap: 0px;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 10px; // Exact Figma border radius
  border-color: variables.$gray-2; // Exact Figma border color
  border-style: solid;
  border-width: 1px;
  background: #ffffff; // Exact Figma background
  height: 48px; // Exact Figma height
  width: 100%;
  outline: none;
  color: variables.$gray-3; // Exact Figma text color
  font-size: 12px; // Exact Figma font size
  font-family: Urbane; // Exact Figma font family
  line-height: 20px; // Exact Figma line height
  font-weight: 300; // Exact Figma font weight
  text-align: left;

  &::placeholder {
    color: variables.$gray-3; // Exact Figma placeholder color
  }

  &:focus {
    border-color: variables.$gray-3;
  }

  &:disabled {
    background-color: variables.$gray-1;
    cursor: not-allowed;
    color: variables.$gray-3;
  }
}

// Calendar component styling - exact Figma specifications
.calendar {
  height: 48px; // Exact Figma height
  width: 100%; // Exact Figma width
}

// Notes component styling - exact Figma specifications
.notes {
  width: 100%; // Exact Figma width
}

// Responsive design
@media (max-width: 768px) {
  .frame-939 {
    flex-direction: column;
    gap: 8px;
  }

  .frame-940,
  .frame-941,
  .frame-937 {
    width: 100%;
  }
}
