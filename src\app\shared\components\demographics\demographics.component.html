<div class="demographics-container">
  <div class="demographics-content">
    <div class="left-section">
      <div class="header-section">
        @if (showBackButton) {
          <button class="back-button" (click)="onBackClick()">
            <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M15.8708 10.5972H4.2041M4.2041 10.5972L9.2041 15.5972M4.2041 10.5972L9.2041 5.59717" stroke="#0071BC" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <span>{{ backButtonText }}</span>
          </button>
        }
        <div class="member-id-section">
          <div class="member-id-value">{{ displayData.memberId }}</div>
          <div class="member-id-label">Member ID</div>
        </div>
      </div>
    </div>
    <div class="right-section">
      <div class="demographics-group basic-demographics">
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.memberName }}</div>
          <div class="demographic-label">Member</div>
        </div>
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.dateOfBirth }}</div>
          <div class="demographic-label">DOB</div>
        </div>
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.gender }}</div>
          <div class="demographic-label">Gender</div>
        </div>
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.lob }}</div>
          <div class="demographic-label">LOB</div>
        </div>
      </div>
      <div class="demographics-group provider-group">
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.providerName }}</div>
          <div class="demographic-label">Provider</div>
        </div>
        <div class="demographic-item">
          <div class="demographic-value">{{ displayData.npi }}</div>
          <div class="demographic-label">NPI</div>
        </div>
      </div>
    </div>
  </div>
</div>
