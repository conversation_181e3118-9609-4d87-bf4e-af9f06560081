@use 'variables' as variables;

.value-input-container {
  display: flex;
  flex-direction: column;
  width: 100%; // Ensure it takes full width of its container

  .value-input-label {
    color: variables.$gray-3;
    font-size: 10px;
    font-family: Urbane;
    line-height: 20px;
    letter-spacing: 0%;
    text-decoration: none;
    font-weight: 300;
    text-align: left;
    text-wrap: nowrap;
    margin-bottom: 4px; // Add some space between label and input
  }

  .value-input {
    padding: 12px;
    box-sizing: border-box;
    overflow: hidden;
    border-radius: 10px;
    border-color: variables.$gray-2;
    border-style: solid;
    border-width: 1px;
    background: #ffffff;
    height: 48px;
    width: 100%;
    outline: none;
    color: #000000; // Ensure all input data is black
    font-size: 12px;
    font-family: Urbane;
    line-height: 20px;
    font-weight: 300;
    text-align: left;

    &::placeholder {
      color: variables.$gray-3;
    }

    &:focus {
      border-color: variables.$gray-3;
    }

    &:disabled {
      background-color: variables.$gray-1;
      cursor: not-allowed;
      color: variables.$gray-3;
    }
  }
}