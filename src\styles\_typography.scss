// Import variables
@use 'variables' as variables;

// Font Face Declarations
@font-face {
  font-family: 'Urbane';
  src: url('../assets/fonts/urbane/Urbane-Light.woff2') format('woff2'),
       url('../assets/fonts/urbane/Urbane-Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Urbane';
  src: url('../assets/fonts/urbane/Urbane-Medium.woff2') format('woff2'),
       url('../assets/fonts/urbane/Urbane-Medium.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Urbane';
  src: url('../assets/fonts/urbane/Urbane-Medium.woff2') format('woff2'),
       url('../assets/fonts/urbane/Urbane-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Urbane';
  src: url('../assets/fonts/urbane/Urbane-DemiBold.woff2') format('woff2'),
       url('../assets/fonts/urbane/Urbane-DemiBold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

// Typography Scale
$font-family-base: 'Urbane', sans-serif;

$font-size-xs: 10px;
$font-size-sm: 11px;
$font-size-base: 12px;
$font-size-md: 14px;
$font-size-lg: 16px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

$font-weight-light: 300;
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;

$line-height-sm: 16px;
$line-height-base: 20px;
$line-height-lg: 32px;

// Typography Mixins
@mixin text-xs {
  font-size: $font-size-xs;
  font-family: $font-family-base;
  line-height: $line-height-base;
}

@mixin text-sm {
  font-size: $font-size-sm;
  font-family: $font-family-base;
  line-height: $line-height-base;
}

@mixin text-base {
  font-size: $font-size-base;
  font-family: $font-family-base;
  line-height: $line-height-base;
}

@mixin text-md {
  font-size: $font-size-md;
  font-family: $font-family-base;
  line-height: $line-height-base;
}

@mixin text-lg {
  font-size: $font-size-lg;
  font-family: $font-family-base;
  line-height: $line-height-base;
}

@mixin text-xl {
  font-size: $font-size-xl;
  font-family: $font-family-base;
  line-height: $line-height-lg;
}

@mixin text-xxl {
  font-size: $font-size-xxl;
  font-family: $font-family-base;
  line-height: $line-height-lg;
}

// Typography Classes
.text-xs {
  @include text-xs;
}

.text-sm {
  @include text-sm;
}

.text-base {
  @include text-base;
}

.text-md {
  @include text-md;
}

.text-lg {
  @include text-lg;
}

.text-xl {
  @include text-xl;
}

.text-xxl {
  @include text-xxl;
}

.font-light {
  font-weight: $font-weight-light;
}

.font-regular {
  font-weight: $font-weight-regular;
}

.font-medium {
  font-weight: $font-weight-medium;
}

.font-semibold {
  font-weight: $font-weight-semibold;
}

// Common Text Styles from the design
.label-text {
  @include text-base;
  font-weight: $font-weight-light;
  color: variables.$text-black;
}

.link-text {
  @include text-base;
  font-weight: $font-weight-medium;
  color: variables.$link;
}

.heading-text {
  @include text-xl;
  font-weight: $font-weight-semibold;
  color: variables.$text-black;
}

.subheading-text {
  @include text-md;
  font-weight: $font-weight-semibold;
  color: variables.$text-black;
}

.caption-text {
  @include text-xs;
  font-weight: $font-weight-medium;
  color: variables.$gray-3;
}

// Apply base typography to body
body {
  font-family: $font-family-base;
  font-size: $font-size-base;
  line-height: $line-height-base;
  color: variables.$text-black;
}