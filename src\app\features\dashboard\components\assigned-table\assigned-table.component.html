<div class="assigned-table-container">
  <div class="assigned-table-header">
    @for (column of columns; track column) {
      <div
        class="assigned-table-header-cell"
        [ngStyle]="{'width': column.width}">
        {{ column.header }}
      </div>
    }
  </div>

  <div class="assigned-table-body">
    @if (filteredCharts.length > 0) {
      @for (chart of filteredCharts; track chart) {
        <div class="assigned-table-row">
          @for (column of columns; track column) {
            <div
              class="assigned-table-cell"
              [ngStyle]="{'width': column.width}">
              @if (column.field !== 'status') {
                {{ getFieldValue(chart, column.field) }}
              } @else {
                <app-button
                  variant="primary"
                  [disabled]="chart.status.trim().toLowerCase() !== 'review'"
                  [figmaExact]="true"
                  (buttonClick)="navigateToChartReview(chart)">
                  {{ chart.status }}
                </app-button>
              }
            </div>
          }
        </div>
      }
    } @else {
      <div class="no-charts-message">
        No charts found matching your criteria.
      </div>
    }

  </div>
</div>
