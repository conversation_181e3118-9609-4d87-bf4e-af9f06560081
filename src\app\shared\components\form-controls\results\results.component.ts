import { Component, Input, Output, EventEmitter, forwardRef, OnInit } from '@angular/core';

import { ControlValueAccessor, NG_VALUE_ACCESSOR, FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { InclusionsTabComponent } from './tabs/inclusions-tab.component';
import { ExclusionsTabComponent } from './tabs/exclusions-tab.component';
import { NoneFoundTabComponent } from './tabs/none-found-tab.component';

export interface ResultsData {
  category: 'inclusions' | 'exclusions' | 'none-found';
  telehealth: boolean;
  systolic: string;
  diastolic: string;
  dateOfService?: string;
  reasoning?: string;
  notes?: string;
}

export interface TabConfig {
  id: 'inclusions' | 'exclusions' | 'none-found';
  label: string;
  active: boolean;
}

@Component({
  selector: 'app-results',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    InclusionsTabComponent,
    ExclusionsTabComponent,
    NoneFoundTabComponent
],
  templateUrl: './results.component.html',
  styleUrls: ['./results.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ResultsComponent),
      multi: true
    }
  ]
})
export class ResultsComponent implements ControlValueAccessor, OnInit {
  @Input() title: string = 'Results';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() errorMessage: string = '';
  @Input() id: string = '';
  @Input() name: string = '';
  @Input() formGroup!: FormGroup;
  // Reasoning options removed - moved to separate dropdown component

  @Output() dataChange = new EventEmitter<ResultsData>();
  @Output() tabChange = new EventEmitter<string>();


  tabs: TabConfig[] = [
    { id: 'inclusions', label: 'Inclusions', active: true },
    { id: 'exclusions', label: 'Exclusions', active: false },
    { id: 'none-found', label: 'None found', active: false }
  ];

  activeTab: 'inclusions' | 'exclusions' | 'none-found' = 'inclusions';



  // ControlValueAccessor implementation
  onChange: any = () => {};
  onTouched: any = () => {};

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    if (!this.id) {
      this.id = 'results-' + Math.random().toString(36).substring(2, 9);
    }

    // Subscribe to form changes
    this.formGroup.valueChanges.subscribe(value => {
      const resultsData: ResultsData = {
        category: this.activeTab,
        telehealth: value.telehealth,
        systolic: value.systolic,
        diastolic: value.diastolic,
        dateOfService: value.dateOfService,
        reasoning: value.reasoning,
        notes: value.notes
      };

      this.onChange(resultsData);
      this.dataChange.emit(resultsData);
    });
  }

  writeValue(value: ResultsData): void {
    if (value) {
      this.activeTab = value.category || 'inclusions';
      this.updateActiveTab(this.activeTab);

      this.formGroup.patchValue({
        category: value.category,
        telehealth: value.telehealth || false,
        systolic: value.systolic|| '',
        diastolic: value.diastolic || '',
        dateOfService: value.dateOfService || '',
        reasoning: value.reasoning || '',
        notes: value.notes || ''
      }, { emitEvent: false });
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    if (isDisabled) {
      this.formGroup.disable();
    } else {
      this.formGroup.enable();
    }
  }

  selectTab(tabId: 'inclusions' | 'exclusions' | 'none-found'): void {
    if (!this.disabled) {
      this.activeTab = tabId;
      this.updateActiveTab(tabId);
      this.formGroup.patchValue({ category: tabId });
      this.tabChange.emit(tabId);
      this.onTouched();
    }
  }

  private updateActiveTab(activeTabId: string): void {
    this.tabs = this.tabs.map(tab => ({
      ...tab,
      active: tab.id === activeTabId
    }));
  }



  onFormFieldChange(): void {
    this.onTouched();
  }
}
