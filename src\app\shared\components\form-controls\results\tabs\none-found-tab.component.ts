import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';

import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { NotesComponent } from '../../../notes/notes.component';
import { DropdownComponent, DropdownOption } from '../../dropdown/dropdown.component';

@Component({
  selector: 'app-none-found-tab',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NotesComponent,
    DropdownComponent
],
  templateUrl: './none-found-tab.component.html',
  styleUrls: ['./none-found-tab.component.scss']
})
export class NoneFoundTabComponent implements OnInit {
  @Input() formGroup!: FormGroup;
  @Input() disabled: boolean = false;
  @Input() id: string = '';

  @Output() fieldChange = new EventEmitter<void>();

  // None Found reasoning options (multi-select) matching Figma specifications
  noneFoundReasoningOptions: DropdownOption[] = [
    { value: 'blood-pressure-values-do-not-match', label: 'Blood pressure values do not match' },
    { value: 'bmi-not-found', label: 'BMI not found' },
    { value: 'chart-summary-not-found', label: 'Chart summary not found' },
    { value: 'dates-do-not-match', label: 'Dates do not match' },
    { value: 'documentation-does-not-match-entry', label: 'Documentation does not match entry' },
    { value: 'insufficient-documentation', label: 'Insufficient documentation' },
    { value: 'insufficient-patient-identifiers', label: 'Insufficient patient identifiers' },
    { value: 'lab-value-not-entered', label: 'Lab value not entered' },
    { value: 'no-documentation', label: 'No documentation' },
    { value: 'out-of-timeframe-for-measurement-period', label: 'Out of timeframe for measurement period' },
    { value: 'patient-name-dob-does-not-match-records', label: 'Patient name/DOB does not match records' },
    { value: 'other', label: 'Other' }
  ];

  constructor() {}

  ngOnInit(): void {
    // Component initialization
  }

  onFormFieldChange(): void {
    this.fieldChange.emit();
  }
}
