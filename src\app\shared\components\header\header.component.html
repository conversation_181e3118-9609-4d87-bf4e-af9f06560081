<!-- Header structure based on chart-review page -->
<header class="chart-header">
  <!-- Top Bar -->
  <div class="top-bar">
    <div class="logo-container">
      <!-- Use the logoUrl input -->
      <img [src]="logoUrl" alt="Stellarus Logo" class="logo-img">
    </div>
    <div class="user-section">
      <!-- Use the userName input -->
      <span class="user-name">{{ userName }}</span>
      <div class="user-avatar-container">
        <!-- Generate avatar initials from userName -->
        <div class="user-avatar">{{ getAvatarInitials() }}</div>
      </div>
      <!-- TODO: Implement dropdown functionality -->
      <span class="dropdown-icon">▼</span>
    </div>
  </div>
  <!-- Note: The second info bar from chart-review is omitted as it's context-specific -->
</header>