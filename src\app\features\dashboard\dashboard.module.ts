import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

import { DashboardRoutingModule } from './dashboard-routing.module';
import { DashboardPageComponent } from './pages/dashboard-page/dashboard-page.component';
import { ChartListComponent } from './components/chart-list/chart-list.component';
import { SearchFilterComponent } from './components/search-filter/search-filter.component';
import { FilterButtonComponent } from './components/filter-button/filter-button.component';
import { SortButtonComponent } from './components/sort-button/sort-button.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DashboardRoutingModule
  ]
})
export class DashboardModule { }
