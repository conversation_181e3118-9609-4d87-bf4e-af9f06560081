import { environment } from '../../../environments/environment';

export interface DatabricksConfig {
  baseUrl: string;
  token: string;
  volumePath: string;
  timeout?: number;
  useProxy?: boolean;        // If true, route via Node server proxy
  proxyBasePath?: string;    // Base path for proxy endpoints (default: '/api/databricks')
}

export const DATABRICKS_CONFIG: DatabricksConfig = {
  baseUrl: environment.databricks.baseUrl?.replace(/\/$/, '') || '',
  token: (environment as any).databricks?.token || '',
  volumePath: environment.databricks.volumePath,
  timeout: (environment as any).databricks?.timeout ?? 30000,
  useProxy: (environment as any).databricks?.useProxy ?? true,
  proxyBasePath: (environment as any).databricks?.proxyBasePath ?? '/api/databricks'
};

export const DATABRICKS_CONFIG_TOKEN = 'DATABRICKS_CONFIG';
