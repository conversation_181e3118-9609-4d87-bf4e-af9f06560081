import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

export interface ReasoningOption {
  value: string;
  label: string;
}

@Component({
  selector: 'app-reasoning-dropdown',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './reasoning-dropdown.component.html',
  styleUrls: ['./reasoning-dropdown.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ReasoningDropdownComponent),
      multi: true
    }
  ]
})
export class ReasoningDropdownComponent implements ControlValueAccessor {
  @Input() label: string = 'Reasoning';
  @Input() placeholder: string = 'Select reasoning';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() errorMessage: string = '';
  @Input() options: ReasoningOption[] = [
    { value: 'frailty', label: 'Frailty' },
    { value: 'dementia', label: 'Dementia' },
    { value: 'advanced-illness', label: 'Advanced Illness' },
    { value: 'institutional-snf', label: 'Institutional SNF' },
    { value: 'other', label: 'Other' }
  ];

  @Output() selectionChange = new EventEmitter<string>();

  value: string = '';
  isOpen: boolean = false;

  // ControlValueAccessor implementation
  onChange: any = () => {};
  onTouched: any = () => {};

  writeValue(value: string): void {
    this.value = value || '';
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  toggleDropdown(): void {
    if (!this.disabled) {
      this.isOpen = !this.isOpen;
      if (this.isOpen) {
        this.onTouched();
      }
    }
  }

  selectOption(option: ReasoningOption): void {
    if (!this.disabled) {
      this.value = option.value;
      this.isOpen = false;
      this.onChange(this.value);
      this.selectionChange.emit(this.value);
      this.onTouched();
    }
  }

  get selectedLabel(): string {
    const selectedOption = this.options.find(option => option.value === this.value);
    return selectedOption ? selectedOption.label : this.placeholder;
  }

  get displayValue(): string {
    return this.value ? this.selectedLabel : this.placeholder;
  }
}
