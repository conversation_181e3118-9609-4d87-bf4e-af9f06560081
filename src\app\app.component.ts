import { Component, OnInit, AfterViewInit, ViewChild, ElementRef, Renderer2 } from '@angular/core';
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit, AfterViewInit {
  title = 'clinical-quality-app';
  @ViewChild(RouterOutlet) routerOutlet!: RouterOutlet;
  
  constructor(
    private router: Router,
    private renderer: Renderer2,
    private el: ElementRef
  ) {
    console.log('AppComponent constructor');
  }
  
  ngOnInit() {
    console.log('AppComponent ngOnInit');
    
    // Log router events to see if there are duplicate navigation events
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      console.log('Navigation event:', event.url);
      console.log('Current router config:', JSON.stringify(this.router.config, (key, value) => {
        if (key === 'component' && typeof value === 'function') {
          return value.name;
        }
        if (key === '_loadedConfig') {
          return {
            routes: value.routes.map((r: any) => ({
              path: r.path,
              component: r.component?.name
            }))
          };
        }
        return value;
      }, 2));
      
      // Check for multiple router outlets after navigation
      if (typeof document !== 'undefined') {
        const routerOutlets = document.querySelectorAll('router-outlet');
        console.log(`Found ${routerOutlets.length} router-outlet elements after navigation to ${event.url}`);
        
        // Log the DOM structure around router outlets
        routerOutlets.forEach((outlet, index) => {
          console.log(`Router outlet #${index + 1} parent:`, outlet.parentElement?.tagName);
          console.log(`Router outlet #${index + 1} siblings:`, outlet.parentElement?.children.length);
        });
      }
    });
  }
  
  ngAfterViewInit() {
    console.log('AppComponent ngAfterViewInit');
    
    // Only try to access the component if the outlet is activated
    if (this.routerOutlet && this.routerOutlet.isActivated) {
      console.log('Router outlet component:', this.routerOutlet.component?.constructor.name);
    } else {
      console.log('Router outlet is not yet activated');
    }
    
    // Check for multiple router outlets
    if (typeof document !== 'undefined') {
      const routerOutlets = document.querySelectorAll('router-outlet');
      console.log(`Found ${routerOutlets.length} router-outlet elements in ngAfterViewInit`);
      
      // Add a MutationObserver to detect DOM changes that might add router outlets
      if (typeof MutationObserver !== 'undefined') {
        const observer = new MutationObserver((mutations) => {
          const currentOutlets = document.querySelectorAll('router-outlet');
          if (currentOutlets.length > 1) {
            console.log(`MutationObserver detected ${currentOutlets.length} router outlets`);
            console.log('DOM mutation detected that might have added router outlets');
          }
        });
        
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      }
    }
  }
}
