import { Component, OnInit, AfterViewInit, inject, ViewChild, PLATFORM_ID } from '@angular/core'; // Import inject, ViewChild, PLATFORM_ID, AfterViewInit
import { ActivatedRoute, RouterModule, Router } from '@angular/router'; // Import RouterModule and Router
import { isPlatformBrowser } from '@angular/common'; // Import isPlatformBrowser
import { FormGroup, FormControl, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { PdfViewerComponent } from '@features/chart-review/components/pdf-viewer/pdf-viewer.component';
import { MenuComponent, UserProfile, MenuItem } from '@shared/components/menu/menu.component';
import { AiHighlightsComponent, AiHighlightData } from '@shared/components/ai-highlights/ai-highlights.component';
import { ResultsComponent } from '@shared/components/form-controls/results/results.component';
import { ButtonComponent } from '@shared/components/buttons/button.component';
import { DemographicsComponent, DemographicsData } from '@shared/components/demographics/demographics.component';
import { HttpClient } from '@angular/common/http';
import { PdfService } from '@features/chart-review/services/pdf.service'; // Import PdfService
import { PageTitleComponent } from '@shared/components/page-title/page-title.component'; // Import PageTitleComponent

interface Finding {
  id: string;
  type: string;
  text: string;
  confidence: number;
  pageNumber: number;
  validated: boolean;
  isValid?: boolean;
}

@Component({
  selector: 'app-chart-review-page',
  standalone: true,
  imports: [
    PdfViewerComponent,
    FormsModule,
    MenuComponent,
    AiHighlightsComponent,
    ResultsComponent,
    ButtonComponent,
    DemographicsComponent,
    RouterModule,
    PageTitleComponent
],
  templateUrl: './chart-review-page.component.html',
  styleUrls: ['./chart-review-page.component.scss']
})
export class ChartReviewPageComponent implements OnInit, AfterViewInit {
  @ViewChild(PdfViewerComponent) pdfViewerComponent!: PdfViewerComponent;
  @ViewChild(AiHighlightsComponent) aiHighlightsComponent!: AiHighlightsComponent;

  chartId: string = '';
  selectedFinding: Finding | null = null;
  // Demographics data for the patient info header (will be updated with route data)
  demographicsData: DemographicsData = {
    memberId: '',
    memberName: '',
    dateOfBirth: '',
    gender: '',
    lob: '',
    providerName: '',
    npi: ''
  };

  // Navigation data
  userProfile: UserProfile = {
    name: 'Jane Chu',
    avatar: ''
  };

  menuItems: MenuItem[] = [
    { label: 'Dashboard', route: '/dashboard', icon: '🏠' },
    { label: 'Profile', route: '/profile', icon: '👤' },
    { label: 'Settings', route: '/settings', icon: '⚙️' },
    { label: 'Help', route: '/help', icon: '❓' },
    { label: 'Logout', action: () => this.logout(), icon: '🚪' }
  ];

  // AI Highlights data for the ai-highlights component
  aiHighlightsData: AiHighlightData[] = [
    {
      id: 'highlight-1',
      measure: 'CBP',
      dateOfService: '07/21/24',
      systolic: 136,
      diastolic: 82,
      page: 2,
      include: false
    },
    {
      id: 'highlight-2',
      measure: 'CBP',
      dateOfService: '07/21/24',
      systolic: 140,
      diastolic: 82,
      page: 2,
      include: false
    },
    {
      id: 'highlight-3',
      measure: 'CBP',
      dateOfService: '05/21/24',
      systolic: 150,
      diastolic: 90,
      page: 7,
      include: false
    }
  ];

  // Results data for the results component
resultsForm: FormGroup = new FormGroup({
  category: new FormControl('inclusions'),
  telehealth: new FormControl(false),
  sys: new FormControl(''),
  dias: new FormControl(''),
  dateOfService: new FormControl(''),
  notes: new FormControl(''),
  reasoning: new FormControl('') // Add this line
});


  // zoom property removed as it's no longer needed
  // TODO: Determine how selectedFinding should be set now that the sidebar is removed.
  // It might come from interactions with the PDF viewer or another source.

  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private pdfService = inject(PdfService); // Inject PdfService
  private http = inject(HttpClient); // Inject HttpClient for loading default PDF
  private platformId = inject(PLATFORM_ID); // Inject platform ID for browser detection

  // Remove the constructor if only used for DI before

  ngOnInit(): void {
    // Get chart ID from route parameters
    this.chartId = this.route.snapshot.paramMap.get('id') || '';

    if (this.chartId) {
      console.log(`[ChartReviewPage] Attempting to load chart with ID: ${this.chartId}`);
      
      // Use the new loadPdfWithFallback method that tries Databricks first, then local assets
      this.pdfService.loadPdfWithFallback(this.chartId).subscribe({
        next: (pdfDoc) => {
          console.log(`[ChartReviewPage] PDF loaded successfully for chart ID: ${this.chartId}, Pages: ${pdfDoc.numPages}`);
          // PdfViewerComponent should automatically react to the PdfService's BehaviorSubject update.
        },
        error: (err) => {
          console.error(`[ChartReviewPage] Error loading PDF for chart ID (${this.chartId}):`, err);
          // TODO: Display a user-friendly error message in the UI.
          // For example, set an error message property and bind it in the template.
        }
      });
    } else {
      console.warn('[ChartReviewPage] No chart ID found in route parameters. Cannot load PDF.');
      // TODO: Optionally, display a message to the user that no chart ID was provided.
    }

    // Update demographics data with member information
    this.updateDemographicsData();

    console.log('[ChartReviewPage] Demographics data initialized:', this.demographicsData);

    // PDF loading is now handled above with loadPdfWithFallback

    // TODO: Initialize selectedFinding based on the new selection mechanism if needed.
    // this.selectedFinding = ...;
  }

  ngAfterViewInit(): void {
    console.log('[ChartReviewPage] AfterViewInit - setting up AI Highlights event subscriptions when the page loads');
    if (this.aiHighlightsComponent) {
      console.log('Either AI Highlights component found, subscribing to events');
      this.aiHighlightsComponent.pageClick.subscribe(event => {
        console.log('*** PAGE CLICK EVENT RECEIVED VIA SUBSCRIPTION ***');
        this.goToPdfPage(event.page);
      });
      this.aiHighlightsComponent.includeChange.subscribe(event => {
        console.log('*** INCLUDE CHANGE EVENT RECEIVED VIA SUBSCRIPTION ***');
        // Handle the include change event, e.g., update resultsData or other logic
      });
    } else {
      console.log('AI Highlights component not found in AfterViewInit');
    }
  }

  /**
   * Handles the file selection event from an input element.
   * This method is for manual file uploads by the user, not for loading charts by ID.
   * Commenting out for now as the primary task is to load by chartId.
   */
  /*
  onFileSelected(event: Event): void {
    console.log('[ChartReviewPage] onFileSelected method called.');
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      console.log(`[ChartReviewPage] File selected: ${file.name}`);
      // loadPdfFromFile is used when a user explicitly selects a file using a file input.
      this.pdfService.loadPdfFromFile(file).subscribe({
        next: (pdfDoc) => {
          console.log(`[ChartReviewPage] PDF loaded successfully by service (from file input): ${pdfDoc.numPages} pages`);
        },
        error: (err) => {
          console.error('[ChartReviewPage] Error loading PDF via service (from file input):', err);
        }
      });
    } else {
      console.log('[ChartReviewPage] No file selected via file input.');
    }
  }
  */

  // Zoom methods removed as requested

  /**
   * Navigates the PDF viewer to a specific page.
   * @param pageNumber The page number to navigate to.
   */
  public goToPdfPage(pageNumber: number): void {
    if (this.pdfViewerComponent) {
      console.log(`[ChartReviewPage] Navigating to PDF page: ${pageNumber}`);
      this.pdfViewerComponent.currentPage = pageNumber;
    } else {
      console.warn('[ChartReviewPage] goToPdfPage called, but pdfViewerComponent is not available.');
    }
  }

  // Navigation event handlers
  onLogoClick(): void {
    console.log('Logo clicked');
    this.router.navigate(['/dashboard']);
  }

  onUserClick(): void {
    console.log('User clicked');
  }

  onDropdownToggle(isOpen: boolean): void {
    console.log('Dropdown toggled:', isOpen);
  }

  onMenuItemClick(item: MenuItem): void {
    console.log('Menu item clicked:', item);
    if (item.route) {
      this.router.navigate([item.route]);
    } else if (item.action) {
      item.action();
    }
  }

  logout(): void {
    console.log('Logout clicked');
    this.router.navigate(['/']);
  }

  // Demographics component event handler
  onDemographicsBackClick(): void {
    console.log('Demographics back button clicked');
    this.router.navigate(['/dashboard']);
  }

  // Update demographics data based on member ID
  private updateDemographicsData(): void {
    const memberId = this.chartId;
    console.log(`[ChartReviewPage] Updating demographics for member ID: ${memberId}`);

    // Update member ID
    this.demographicsData.memberId = memberId || 'Unknown';

    // Set member-specific data based on member ID
    if (memberId === '55820474') {
      this.demographicsData.memberName = 'John Dey';
      this.demographicsData.dateOfBirth = '01/05/1972';
      this.demographicsData.gender = 'M';
      this.demographicsData.lob = 'PPO';
      this.demographicsData.providerName = 'Nicolas Dejong';
      this.demographicsData.npi = '882716229';
    } else {
      // Default data for other member IDs
      this.demographicsData.memberName = 'Sample Patient';
      this.demographicsData.dateOfBirth = '01/01/1970';
      this.demographicsData.gender = 'U';
      this.demographicsData.lob = 'HMO';
      this.demographicsData.providerName = 'Dr. Sample Provider';
      this.demographicsData.npi = '123456789';
    }

    console.log('[ChartReviewPage] Demographics updated:', this.demographicsData);
  }

  // Old PDF loading methods removed - now using loadPdfWithFallback in ngOnInit

  // AI Highlights component event handlers
  onAiHighlightsDataChange(data: AiHighlightData[]) {
    this.aiHighlightsData = data;
  }

  onAiHighlightsPageClick(event: { highlight: AiHighlightData, page: number }) {
    // Implement navigation or highlight logic as needed
    // Example: this.pdfViewerComponent.goToPage(event.page);
  }

  onAiHighlightsIncludeChange(event: { highlight: AiHighlightData; include: boolean }): void {
    console.log('AI Highlights include changed:', event);

    const { highlight, include } = event;

  console.log('🔁 Checkbox toggled. Include:', include);
  console.log('📥 Incoming highlight data:', highlight);
  console.log('📋 Form before patch:', this.resultsForm?.value);
  console.log('🧪 Has dateOfService control?', this.resultsForm.contains('dateOfService')); // ✅ convert to Date object
 
   if (include) {
     this.resultsForm.patchValue({
       sys: String(highlight.systolic),
       dias: String(highlight.diastolic),
       dateOfService: highlight.dateOfService
     });
   }
 
   console.log('✅ Form after patch:', this.resultsForm?.value);
   console.log('📅 dateOfService now holds:', this.resultsForm.get('dateOfService')?.value);
}
onCompleteReview(): void {
  console.log('Complete review clicked');
  console.log('Results form values:', this.resultsForm.value);

  // Add save/submit logic using this.resultsForm.value
}
}