@use 'variables' as variables;
@use 'mixins' as mix;

.chart-header {
  background: variables.$light-background;
  display: flex;
  flex-direction: column;
  width: 100%;

  .top-bar {
    height: 80px;
    background: variables.$white;
    border-bottom: variables.$border-width-default variables.$gray-1 solid;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
  }

  .logo-container {
    .logo-img {
      width: 150px;
      height: auto; // Maintain aspect ratio
    }
  }

  .user-section {
    display: flex;
    align-items: center;
    gap: variables.$spacing-md;
  }

  .user-name {
    color: variables.$text-black;
    font-size: 12px;
    font-family: 'Urbane', sans-serif;
    font-weight: 300;
  }

  .user-avatar-container {
    padding: variables.$spacing-xs;
    background: rgba(56, 112, 184, 0.20);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .user-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: variables.$primary-blue;
    color: variables.$white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 10px; // Smaller font for initials
  }

  .dropdown-icon {
    color: variables.$gray-3;
    cursor: pointer;
  }

  // Note: Info bar styles are omitted as they are specific to chart-review
}