{"name": "clinical-quality-app", "version": "0.0.0", "scripts": {"ng": "ng", "copy-pdf-worker": "node copy-pdf-worker.js", "prestart": "npm run copy-pdf-worker", "start": "node dist/clinical-quality-app/server/server.mjs", "dev": "ng serve", "prebuild": "npm run copy-pdf-worker", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:clinical-quality-app": "node dist/clinical-quality-app/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^20.0.5", "@angular/cdk": "^19.2.10", "@angular/common": "^20.0.5", "@angular/compiler": "^20.0.5", "@angular/core": "^20.0.5", "@angular/forms": "^20.0.5", "@angular/material": "^19.2.10", "@angular/platform-browser": "^20.0.5", "@angular/platform-browser-dynamic": "^20.0.5", "@angular/platform-server": "^20.0.5", "@angular/router": "^20.0.5", "@angular/ssr": "^20.0.4", "@ngrx/effects": "^20.0.0", "@ngrx/entity": "^20.0.0", "@ngrx/store": "^20.0.0", "@ngrx/store-devtools": "^20.0.0", "@types/node-fetch": "^2.6.13", "express": "^4.18.2", "idb": "^8.0.2", "ngx-extended-pdf-viewer": "^23.1.1", "node-fetch": "^3.3.2", "papaparse": "^5.5.3", "pdfjs-dist": "^5.2.133", "rxjs": "~7.8.0", "tslib": "^2.3.0", "uuid": "^11.1.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^20.0.4", "@angular/cli": "^20.0.4", "@angular/compiler-cli": "^20.0.5", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "@types/papaparse": "^5.3.16", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.3"}}