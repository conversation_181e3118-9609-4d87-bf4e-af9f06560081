import { Component, Input, Output, EventEmitter } from '@angular/core';

import { Router, RouterModule } from '@angular/router';

export interface UserProfile {
  name: string;
  avatar?: string;
}

export interface MenuItem {
  label: string;
  route?: string;
  action?: () => void;
  icon?: string;
}

@Component({
  selector: 'app-menu',
  standalone: true,
  imports: [RouterModule],
  templateUrl: './menu.component.html',
  styleUrls: ['./menu.component.scss']
})
export class MenuComponent {
  @Input() logoSrc: string = '';
  @Input() logoAlt: string = 'Logo';
  @Input() user: UserProfile | null = null;
  @Input() menuItems: MenuItem[] = [];
  @Input() showUserDropdown: boolean = true;

  @Output() logoClick = new EventEmitter<void>();
  @Output() userClick = new EventEmitter<void>();
  @Output() dropdownToggle = new EventEmitter<boolean>();
  @Output() menuItemClick = new EventEmitter<MenuItem>();

  isDropdownOpen: boolean = false;

  constructor(private router: Router) {}

  onLogoClick(): void {
    this.logoClick.emit();
    // Default behavior: navigate to dashboard
    this.router.navigate(['/dashboard']);
  }

  onUserClick(): void {
    this.userClick.emit();
  }

  onDropdownToggle(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
    this.dropdownToggle.emit(this.isDropdownOpen);
  }

  onMenuItemClick(item: MenuItem): void {
    this.menuItemClick.emit(item);
    
    if (item.route) {
      this.router.navigate([item.route]);
    } else if (item.action) {
      item.action();
    }
    
    // Close dropdown after selection
    this.isDropdownOpen = false;
  }

  // Close dropdown when clicking outside
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = document.querySelector('.user-dropdown');
    
    if (dropdown && !dropdown.contains(target)) {
      this.isDropdownOpen = false;
    }
  }
}
