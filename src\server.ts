import { APP_BASE_HREF } from '@angular/common';
import { CommonEngine, isMainModule } from '@angular/ssr/node';
import express from 'express';
import { dirname, join, resolve } from 'node:path';
import { fileURLToPath } from 'node:url';
import bootstrap from './main.server';
import fetch from 'node-fetch';

const serverDistFolder = dirname(fileURLToPath(import.meta.url));
const browserDistFolder = resolve(serverDistFolder, '../browser');
const indexHtml = join(serverDistFolder, 'index.server.html');

const app = express();
const commonEngine = new CommonEngine();

// Parse JSON bodies
app.use(express.json({ limit: '10mb' }));

// Databricks proxy to avoid CORS and secure token usage
const DATABRICKS_BASE_URL = process.env['DATABRICKS_BASE_URL']?.replace(/\/$/, '') || '';
const DATABRICKS_TOKEN = process.env['DATABRICKS_TOKEN'] || '';
const DATABRICKS_VOLUME_PATH = process.env['DATABRICKS_VOLUME_PATH'] || '/Volumes/poc/rh_code_extraction/front_end_test';

function databricksHeaders() {
  return {
    'Authorization': `Bearer ${DATABRICKS_TOKEN}`,
    'Content-Type': 'application/json'
  } as Record<string, string>;
}

app.post('/api/databricks/fs/list', async (req, res) => {
  try {
    const path = (req.body?.path as string) || DATABRICKS_VOLUME_PATH;
    const resp = await fetch(`${DATABRICKS_BASE_URL}/api/2.0/fs/list`, {
      method: 'POST',
      headers: databricksHeaders(),
      body: JSON.stringify({ path })
    });
    const data = await resp.json();
    res.status(resp.status).send(data);
  } catch (e: any) {
    res.status(500).send({ error: e?.message || 'Proxy error' });
  }
});

app.post('/api/databricks/fs/get-status', async (req, res) => {
  try {
    const path = (req.body?.path as string) || DATABRICKS_VOLUME_PATH;
    const resp = await fetch(`${DATABRICKS_BASE_URL}/api/2.0/fs/get-status`, {
      method: 'POST',
      headers: databricksHeaders(),
      body: JSON.stringify({ path })
    });
    const data = await resp.json();
    res.status(resp.status).send(data);
  } catch (e: any) {
    res.status(500).send({ error: e?.message || 'Proxy error' });
  }
});

app.post('/api/databricks/fs/download', async (req, res) => {
  try {
    const path = (req.body?.path as string) || DATABRICKS_VOLUME_PATH;
    const resp = await fetch(`${DATABRICKS_BASE_URL}/api/2.0/fs/download`, {
      method: 'POST',
      headers: databricksHeaders(),
      body: JSON.stringify({ path })
    });

    if (!resp.ok) {
      const text = await resp.text();
      res.status(resp.status).send(text);
      return;
    }

    // Pass through arraybuffer (binary) response
    const buf = Buffer.from(await resp.arrayBuffer());
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Length', buf.length.toString());
    res.status(200).send(buf);
  } catch (e: any) {
    res.status(500).send({ error: e?.message || 'Proxy error' });
  }
});

/**
 * Serve static files from /browser
 */
app.get(
  '**',
  express.static(browserDistFolder, {
    maxAge: '1y',
    index: 'index.html'
  }),
);

/**
 * Handle all other requests by rendering the Angular application.
 */
app.get('**', (req, res, next) => {
  const { protocol, originalUrl, baseUrl, headers } = req;

  commonEngine
    .render({
      bootstrap,
      documentFilePath: indexHtml,
      url: `${protocol}://${headers.host}${originalUrl}`,
      publicPath: browserDistFolder,
      providers: [{ provide: APP_BASE_HREF, useValue: baseUrl }],
    })
    .then((html) => res.send(html))
    .catch((err) => next(err));
});

/**
 * Start the server if this module is the main entry point.
 * The server listens on the port defined by the `PORT` environment variable, or defaults to 4000.
 * Binds to 0.0.0.0 to be accessible in containerized environments like Databricks Apps.
 */
if (isMainModule(import.meta.url)) {
  const port = parseInt(process.env['PORT'] || '4000', 10);
  const host = '0.0.0.0'; // Bind to all interfaces for Databricks Apps
  app.listen(port, host, () => {
    console.log(`Node Express server listening on http://${host}:${port}`);
  });
}

export default app;
