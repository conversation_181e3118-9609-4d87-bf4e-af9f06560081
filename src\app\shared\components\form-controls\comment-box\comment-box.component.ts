import { Component, Input, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-comment-box',
  templateUrl: './comment-box.component.html',
  styleUrls: ['./comment-box.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CommentBoxComponent),
      multi: true
    }
  ]
})
export class CommentBoxComponent implements ControlValueAccessor {
  @Input() property1: 'Default' | 'Active' | 'Entered' = 'Default';
  @Input() placeholder: string = 'Comment';
  @Input() disabled: boolean = false;

  value: string = '';
  isFocused: boolean = false;

  // ControlValueAccessor implementation
  onChange: any = () => {};
  onTouched: any = () => {};

  writeValue(value: string): void {
    this.value = value || '';
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.value = target.value;
    this.onChange(this.value);
  }

  onFocus(): void {
    this.isFocused = true;
    this.property1 = 'Active';
  }

  onBlur(): void {
    this.isFocused = false;
    this.onTouched();
    this.property1 = this.value ? 'Entered' : 'Default';
  }

  get currentState(): 'Default' | 'Active' | 'Entered' {
    if (this.isFocused) return 'Active';
    if (this.value) return 'Entered';
    return 'Default';
  }
}
