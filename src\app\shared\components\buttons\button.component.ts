import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-button',
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.scss'],
  imports: [CommonModule, NgClass],
  standalone: true
})
export class ButtonComponent {
  @Input() variant: 'primary' | 'secondary' | 'tertiary' = 'primary';
  @Input() disabled: boolean = false;
  @Input() type: 'button' | 'submit' | 'reset' = 'button';
  @Input() icon: string | null = null;
  @Input() iconPosition: 'left' | 'right' = 'left';
  @Input() figmaExact: boolean = false; // Enable Figma-exact sizing
  @Input() figmaState: 'inactive' | 'default' | 'hover' | 'click' | null = null; // Force specific Figma state for demo
  @Output() buttonClick = new EventEmitter<MouseEvent>();

  isActive: boolean = false;

  onClick(event: MouseEvent): void {
    if (!this.disabled && this.figmaState !== 'inactive') {
      this.buttonClick.emit(event);
    }
  }

  onMouseDown(): void {
    this.isActive = true;
  }

  onMouseUp(): void {
    this.isActive = false;
  }

  onMouseLeave(): void {
    this.isActive = false;
  }

  getIconColor(): 'default' | 'hover' | 'click' {
    if (this.figmaState === 'click' || this.isActive) {
      return 'click';
    }
    if (this.figmaState === 'hover') {
      return 'hover';
    }
    return 'default';
  }
}