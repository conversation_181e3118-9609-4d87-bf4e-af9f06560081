@use '@angular/material' as mat;
@use 'variables' as variables;
@use 'sass:color';
@use 'sass:map';

// Include core styles
@include mat.core();

// In Angular Material v19, theming is done using the mat.theme mixin
// instead of the older define-light-theme function

// Use one of the predefined palettes from Angular Material v19
// The documentation shows these are available: $red-palette, $green-palette, $blue-palette, etc.
html {
  color-scheme: light dark;
  @include mat.theme((
    color: (
      // Use the blue palette since our primary color is blue
      primary: mat.$blue-palette,
      tertiary: mat.$cyan-palette,
      theme-type: light,
    ),
    typography: 'Urbane',
    density: 0
  ));
}

// Apply surface colors to body
body {
  background: var(--mat-sys-surface);
  color: var(--mat-sys-on-surface);
}
