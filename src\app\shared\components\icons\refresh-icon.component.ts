import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-refresh-icon',
  template: `
    <svg
      width="16"
      height="16"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      [ngClass]="'refresh-icon-' + color"
    >
      <path d="M9.9993 18.3332C14.6017 18.3332 18.3327 14.6022 18.3327 9.9998C18.3327 5.3975 14.6017 1.6665 9.9993 1.6665C5.397 1.6665 1.666 5.3975 1.666 9.9998C1.666 14.6022 5.397 18.3332 9.9993 18.3332Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M6.6758 12.0917C6.8258 12.3417 7.0091 12.5751 7.2174 12.7834C8.7508 14.3167 11.2424 14.3167 12.7841 12.7834C13.4091 12.1584 13.7674 11.3667 13.8841 10.5583" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M6.1172 9.4417C6.2339 8.625 6.5922 7.8417 7.2172 7.2167C8.7505 5.6833 11.2422 5.6833 12.7839 7.2167C13.0005 7.4333 13.1755 7.6667 13.3255 7.9083" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M6.5176 14.3165V12.0916H8.7426" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M13.4848 5.6831V7.9081H11.2598" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  `,
  styleUrls: ['./refresh-icon.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class RefreshIconComponent {
  @Input() color: 'default' | 'hover' | 'click' = 'default';
}
