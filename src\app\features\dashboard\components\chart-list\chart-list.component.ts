import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router'; // Import Router and RouterModule

@Component({
  selector: 'app-chart-list',
  standalone: true,
  imports: [CommonModule, RouterModule], // Add RouterModule
  templateUrl: './chart-list.component.html',
  styleUrls: ['./chart-list.component.scss']
})
export class ChartListComponent {
  @Input() charts: any[] = [];
  @Input() searchText: string = '';

  // Column definitions for the chart list
  columns = [
    { field: 'memberId', header: 'Member ID', width: 'flex-1' },
    { field: 'name', header: 'Name', width: '200px' },
    { field: 'dob', header: 'DOB', width: '155px' },
    { field: 'lob', header: 'LOB', width: '100px' },
    { field: 'measure', header: 'Measure', width: '93px' },
    { field: 'review1', header: 'Review 1', width: '140px' },
    { field: 'review2', header: 'Review 2', width: '140px' },
    { field: 'assigned', header: 'Assigned', width: '170px' },
    { field: 'status', header: 'Status', width: '118px' }
  ];

  constructor(private router: Router) { } // Inject Router

  // Method to navigate to chart review page
  navigateToChartReview(chart: any): void {
    if (chart && chart.status && chart.status.toLowerCase() === 'review' && chart.memberId) {
      // Assuming chart.memberId can be used as a unique identifier for the chart.
      // This ID will be used to fetch the correct PDF.
      this.router.navigate(['/chart-review', chart.memberId]);
    } else if (chart && chart.status && chart.status.toLowerCase() === 'review' && !chart.memberId) {
      console.error('Chart data is missing memberId for navigation but status is review', chart);
    }
  }

  // Method to filter charts based on search text
  get filteredCharts() {
    if (!this.searchText) {
      return this.charts;
    }
    
    const searchLower = this.searchText.toLowerCase();
    return this.charts.filter(chart => {
      return Object.values(chart).some(value =>
        value && typeof value === 'string' ?
          value.toLowerCase().includes(searchLower) :
          String(value).toLowerCase().includes(searchLower)
      );
    });
  }

  // Method to get status class based on status value
  getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'review':
        return 'status-review';
      case 'complete':
        return 'status-complete';
      default:
        return '';
    }
  }
}