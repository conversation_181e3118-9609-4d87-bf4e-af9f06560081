@use 'variables' as vars;
@use 'mixins' as mix;

.pagination-container {
  width: 100%;
  padding-top: 16px;
  border-top: 1px var(--light-borders, #F1F5F7) solid;
  display: flex;
  justify-content: center;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.pagination-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: var(--white, white);
  border-radius: 8px;
  border: 1px solid var(--gray-2, #D9E1E7);
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  color: var(--text-black, #17181A);
  
  &.disabled {
    color: var(--gray-2, #D9E1E7);
    cursor: not-allowed;
  }
  
  &:hover:not(.disabled) {
    background-color: var(--light-background, #F9FBFC);
  }
}

.pagination-pages {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  justify-content: center;
}

.pagination-page-button {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--white, white);
  border-radius: 8px;
  border: 1px solid var(--gray-2, #D9E1E7);
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  color: var(--text-black, #17181A);
  
  &.active {
    background-color: var(--white, white);
    border-color: var(--text-black, #17181A);
  }
  
  &:not(.active) {
    color: var(--gray-2, #D9E1E7);
  }
  
  &:hover:not(.active) {
    background-color: var(--light-background, #F9FBFC);
  }
}

.pagination-icon {
  width: 8px;
  height: 14px;
  position: relative;
  
  &.prev-icon::before {
    content: '';
    position: absolute;
    width: 8px;
    height: 14px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='14' viewBox='0 0 8 14'%3E%3Cpath d='M7 1L1 7L7 13' stroke='%23D9E1E7' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
  }
  
  &.next-icon::before {
    content: '';
    position: absolute;
    width: 8px;
    height: 14px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='14' viewBox='0 0 8 14'%3E%3Cpath d='M1 1L7 7L1 13' stroke='%2317181A' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
  }
}