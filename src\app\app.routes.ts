import { Routes } from '@angular/router';
import { PdfViewerTestComponent } from '@features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component';
import { ComponentTestComponent } from '@shared/components/component-test/component-test.component';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    loadChildren: () => import('@features/dashboard/dashboard.module').then(m => m.DashboardModule)
  },
  {
    path: 'chart-review',
    loadChildren: () => import('@features/chart-review/chart-review.module').then(m => m.ChartReviewModule)
  },
  {
    path: 'auth',
    loadChildren: () => import('@features/auth/auth.module').then(m => m.AuthModule)
  },
  {
    path: 'pdf-test',
    component: PdfViewerTestComponent
  },
  {
    path: 'component-test',
    component: ComponentTestComponent
  }
];
