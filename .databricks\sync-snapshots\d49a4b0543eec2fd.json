{"version": "v1", "host": "https://adb-640321604414221.1.azuredatabricks.net", "remote_path": "/Workspace/Users/<USER>/cce-js-test", "last_modified_times": {".editorconfig": "2025-07-11T09:21:52.5937386-07:00", ".gitignore": "2025-07-11T09:21:52.6271426-07:00", "DATABRICKS_INTEGRATION.md": "2025-07-14T12:10:43.0234141-07:00", "README.md": "2025-07-11T09:21:52.8673158-07:00", "angular.json": "2025-07-11T09:21:52.6611722-07:00", "app.yaml": "2025-08-14T11:17:07.7002945-07:00", "azure-quick-deploy.md": "2025-07-11T09:21:52.6893615-07:00", "copy-pdf-worker.js": "2025-07-11T09:21:52.7275518-07:00", "databricks-deployment-alternative.md": "2025-07-11T09:21:52.7562869-07:00", "deployment-guide.md": "2025-07-11T09:21:52.7832096-07:00", "package.json": "2025-08-14T11:14:07.9654513-07:00", "public/favicon.ico": "2025-07-11T09:20:16.2319401-07:00", "src/app/app.component.html": "2025-07-11T09:20:16.5120189-07:00", "src/app/app.component.scss": "2025-07-11T09:20:16.5450334-07:00", "src/app/app.component.spec.ts": "2025-07-11T09:20:16.5850328-07:00", "src/app/app.component.ts": "2025-07-11T09:20:16.6231361-07:00", "src/app/app.config.server.ts": "2025-07-11T09:20:16.6727646-07:00", "src/app/app.config.ts": "2025-07-14T12:09:24.0946088-07:00", "src/app/app.routes.ts": "2025-07-11T09:20:16.7677124-07:00", "src/app/core/config/databricks.config.ts": "2025-08-13T10:48:58.8227727-07:00", "src/app/core/core.module.ts": "2025-07-11T09:20:16.8339517-07:00", "src/app/core/data/data.module.ts": "2025-07-11T09:20:16.9040824-07:00", "src/app/core/data/models/chart-data.models.ts": "2025-07-11T09:20:16.9691903-07:00", "src/app/core/data/services/csv-data.service.ts": "2025-07-11T09:20:17.0407783-07:00", "src/app/core/data/services/storage.service.spec.ts": "2025-07-11T09:20:17.0839191-07:00", "src/app/core/data/services/storage.service.ts": "2025-07-11T09:20:17.1241493-07:00", "src/app/core/services/databricks.service.ts": "2025-08-14T11:04:22.9701698-07:00", "src/app/features/auth/auth-routing.module.ts": "2025-07-11T09:20:17.2134007-07:00", "src/app/features/auth/auth.module.ts": "2025-07-11T09:20:17.2538602-07:00", "src/app/features/auth/components/login/login.component.html": "2025-07-11T09:20:17.3456022-07:00", "src/app/features/auth/components/login/login.component.scss": "2025-07-11T09:20:17.3798886-07:00", "src/app/features/auth/components/login/login.component.spec.ts": "2025-07-11T09:20:17.4248884-07:00", "src/app/features/auth/components/login/login.component.ts": "2025-07-11T09:20:17.4659453-07:00", "src/app/features/auth/pages/login-page/login-page.component.html": "2025-07-11T09:20:17.5801354-07:00", "src/app/features/auth/pages/login-page/login-page.component.scss": "2025-07-11T09:20:17.6118903-07:00", "src/app/features/auth/pages/login-page/login-page.component.spec.ts": "2025-07-11T09:20:17.6534102-07:00", "src/app/features/auth/pages/login-page/login-page.component.ts": "2025-07-11T09:20:17.6981902-07:00", "src/app/features/auth/services/auth.service.spec.ts": "2025-07-11T09:20:17.7932322-07:00", "src/app/features/auth/services/auth.service.ts": "2025-07-11T09:20:17.8332206-07:00", "src/app/features/chart-review/chart-review-routing.module.ts": "2025-07-11T09:20:17.8962558-07:00", "src/app/features/chart-review/chart-review.module.ts": "2025-07-11T09:20:17.9393258-07:00", "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.html": "2025-07-11T09:20:18.2612193-07:00", "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.scss": "2025-07-11T09:20:18.2948247-07:00", "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.ts": "2025-07-11T09:20:18.3349674-07:00", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.html": "2025-07-11T09:20:18.0690913-07:00", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.scss": "2025-07-11T09:20:18.1050123-07:00", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.spec.ts": "2025-07-11T09:20:18.145031-07:00", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.ts": "2025-07-11T09:20:18.1880138-07:00", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.html": "2025-07-11T09:20:18.4639458-07:00", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.scss": "2025-07-11T09:20:18.5039041-07:00", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.spec.ts": "2025-07-11T09:20:18.5459998-07:00", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.ts": "2025-07-14T12:09:12.8235591-07:00", "src/app/features/chart-review/services/annotation.service.spec.ts": "2025-07-11T09:20:18.6527097-07:00", "src/app/features/chart-review/services/annotation.service.ts": "2025-07-11T09:20:18.6949004-07:00", "src/app/features/chart-review/services/pdf.service.spec.ts": "2025-07-11T09:20:18.7362719-07:00", "src/app/features/chart-review/services/pdf.service.ts": "2025-07-14T12:08:18.6243624-07:00", "src/app/features/dashboard/components/assigned-table/assigned-table.component.html": "2025-07-11T09:20:18.9889914-07:00", "src/app/features/dashboard/components/assigned-table/assigned-table.component.scss": "2025-07-11T09:20:19.0231091-07:00", "src/app/features/dashboard/components/assigned-table/assigned-table.component.ts": "2025-07-11T09:20:19.070337-07:00", "src/app/features/dashboard/components/chart-list/chart-list.component.html": "2025-07-11T09:20:19.1467626-07:00", "src/app/features/dashboard/components/chart-list/chart-list.component.scss": "2025-07-11T09:20:19.182763-07:00", "src/app/features/dashboard/components/chart-list/chart-list.component.ts": "2025-07-11T09:20:19.229463-07:00", "src/app/features/dashboard/components/filter-button/filter-button.component.html": "2025-07-11T09:20:19.3071322-07:00", "src/app/features/dashboard/components/filter-button/filter-button.component.scss": "2025-07-11T09:20:19.3478925-07:00", "src/app/features/dashboard/components/filter-button/filter-button.component.ts": "2025-07-11T09:20:19.3949628-07:00", "src/app/features/dashboard/components/measure-summary/measure-summary.component.html": "2025-07-11T09:20:19.4870148-07:00", "src/app/features/dashboard/components/measure-summary/measure-summary.component.scss": "2025-07-11T09:20:19.5240297-07:00", "src/app/features/dashboard/components/measure-summary/measure-summary.component.spec.ts": "2025-07-11T09:20:19.5712113-07:00", "src/app/features/dashboard/components/measure-summary/measure-summary.component.ts": "2025-07-11T09:20:19.6122101-07:00", "src/app/features/dashboard/components/pagination/pagination.component.html": "2025-07-11T09:20:19.6917397-07:00", "src/app/features/dashboard/components/pagination/pagination.component.scss": "2025-07-11T09:20:19.7266061-07:00", "src/app/features/dashboard/components/pagination/pagination.component.ts": "2025-07-11T09:20:19.769604-07:00", "src/app/features/dashboard/components/search-filter/search-filter.component.html": "2025-07-11T09:20:19.8417081-07:00", "src/app/features/dashboard/components/search-filter/search-filter.component.scss": "2025-07-11T09:20:19.8807224-07:00", "src/app/features/dashboard/components/search-filter/search-filter.component.ts": "2025-07-11T09:20:19.9286525-07:00", "src/app/features/dashboard/components/sort-button/sort-button.component.html": "2025-07-11T09:20:20.0077429-07:00", "src/app/features/dashboard/components/sort-button/sort-button.component.scss": "2025-07-11T09:20:20.0413075-07:00", "src/app/features/dashboard/components/sort-button/sort-button.component.ts": "2025-07-11T09:20:20.088479-07:00", "src/app/features/dashboard/dashboard-routing.module.ts": "2025-07-11T09:20:18.8505679-07:00", "src/app/features/dashboard/dashboard.module.ts": "2025-07-11T09:20:18.8897157-07:00", "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.html": "2025-07-11T09:20:20.1951846-07:00", "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.scss": "2025-07-11T09:20:20.2330081-07:00", "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.spec.ts": "2025-07-11T09:20:20.2885533-07:00", "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.ts": "2025-07-11T09:20:20.3385073-07:00", "src/app/shared/components/ai-highlights/ai-highlights.component.html": "2025-07-11T09:20:20.5453267-07:00", "src/app/shared/components/ai-highlights/ai-highlights.component.scss": "2025-07-11T09:20:20.5824723-07:00", "src/app/shared/components/ai-highlights/ai-highlights.component.ts": "2025-07-11T09:20:20.6231535-07:00", "src/app/shared/components/buttons/button.component.html": "2025-07-11T09:20:20.6961058-07:00", "src/app/shared/components/buttons/button.component.scss": "2025-07-11T09:20:20.7426575-07:00", "src/app/shared/components/buttons/button.component.ts": "2025-07-11T09:20:20.8218835-07:00", "src/app/shared/components/component-test/component-test.component.html": "2025-07-11T09:20:20.9259293-07:00", "src/app/shared/components/component-test/component-test.component.scss": "2025-07-11T09:20:20.9677375-07:00", "src/app/shared/components/component-test/component-test.component.ts": "2025-07-11T09:20:21.0205956-07:00", "src/app/shared/components/demographics/demographics.component.html": "2025-07-11T09:20:21.0947417-07:00", "src/app/shared/components/demographics/demographics.component.scss": "2025-07-11T09:20:21.1293004-07:00", "src/app/shared/components/demographics/demographics.component.ts": "2025-07-11T09:20:21.1738694-07:00", "src/app/shared/components/form-controls/calendar/calendar.component.html": "2025-07-11T09:20:21.2808072-07:00", "src/app/shared/components/form-controls/calendar/calendar.component.scss": "2025-07-11T09:20:21.3135671-07:00", "src/app/shared/components/form-controls/calendar/calendar.component.ts": "2025-07-11T09:20:21.359829-07:00", "src/app/shared/components/form-controls/checkbox/checkbox.component.html": "2025-07-11T09:20:21.4365196-07:00", "src/app/shared/components/form-controls/checkbox/checkbox.component.scss": "2025-07-11T09:20:21.4677935-07:00", "src/app/shared/components/form-controls/checkbox/checkbox.component.ts": "2025-07-11T09:20:21.5147919-07:00", "src/app/shared/components/form-controls/comment-box/comment-box.component.html": "2025-07-11T09:20:21.588509-07:00", "src/app/shared/components/form-controls/comment-box/comment-box.component.scss": "2025-07-11T09:20:21.6205889-07:00", "src/app/shared/components/form-controls/comment-box/comment-box.component.ts": "2025-07-11T09:20:21.6659352-07:00", "src/app/shared/components/form-controls/dropdown/dropdown.component.html": "2025-07-11T09:20:21.7495607-07:00", "src/app/shared/components/form-controls/dropdown/dropdown.component.scss": "2025-07-11T09:20:21.7852624-07:00", "src/app/shared/components/form-controls/dropdown/dropdown.component.ts": "2025-07-11T09:20:21.8354359-07:00", "src/app/shared/components/form-controls/reasoning-dropdown/reasoning-dropdown.component.ts": "2025-07-11T09:20:21.9106311-07:00", "src/app/shared/components/form-controls/results/results.component.html": "2025-07-11T09:20:21.9869478-07:00", "src/app/shared/components/form-controls/results/results.component.scss": "2025-07-11T09:20:22.0199512-07:00", "src/app/shared/components/form-controls/results/results.component.ts": "2025-07-11T09:20:22.0809496-07:00", "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.html": "2025-07-11T09:20:22.1744697-07:00", "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.scss": "2025-07-11T09:20:22.2079555-07:00", "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.ts": "2025-07-11T09:20:22.2520117-07:00", "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.html": "2025-07-11T09:20:22.3091559-07:00", "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.scss": "2025-07-11T09:20:22.3512987-07:00", "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.ts": "2025-07-11T09:20:22.4105023-07:00", "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.html": "2025-07-11T09:20:22.4731219-07:00", "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.scss": "2025-07-11T09:20:22.5141216-07:00", "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.ts": "2025-07-11T09:20:22.5631195-07:00", "src/app/shared/components/form-controls/value-input/value-input.component.html": "2025-07-11T09:20:22.6688041-07:00", "src/app/shared/components/form-controls/value-input/value-input.component.scss": "2025-07-11T09:20:22.7059007-07:00", "src/app/shared/components/form-controls/value-input/value-input.component.ts": "2025-07-11T09:20:22.7518015-07:00", "src/app/shared/components/header/header.component.html": "2025-07-11T09:20:22.8267909-07:00", "src/app/shared/components/header/header.component.scss": "2025-07-11T09:20:22.8596404-07:00", "src/app/shared/components/header/header.component.ts": "2025-07-11T09:20:22.9036472-07:00", "src/app/shared/components/hits/hits.component.html": "2025-07-11T09:20:22.9772843-07:00", "src/app/shared/components/hits/hits.component.scss": "2025-07-11T09:20:23.0075591-07:00", "src/app/shared/components/hits/hits.component.ts": "2025-07-11T09:20:23.0474412-07:00", "src/app/shared/components/icons/icon.component.html": "2025-07-11T09:20:23.1135243-07:00", "src/app/shared/components/icons/icon.component.scss": "2025-07-11T09:20:23.1476284-07:00", "src/app/shared/components/icons/icon.component.ts": "2025-07-11T09:20:23.1888867-07:00", "src/app/shared/components/icons/refresh-icon.component.scss": "2025-07-11T09:20:23.2244933-07:00", "src/app/shared/components/icons/refresh-icon.component.ts": "2025-07-11T09:20:23.264494-07:00", "src/app/shared/components/menu/menu.component.html": "2025-07-11T09:20:23.3353253-07:00", "src/app/shared/components/menu/menu.component.scss": "2025-07-11T09:20:23.371988-07:00", "src/app/shared/components/menu/menu.component.ts": "2025-07-11T09:20:23.4199879-07:00", "src/app/shared/components/notes/notes.component.html": "2025-07-11T09:20:23.487706-07:00", "src/app/shared/components/notes/notes.component.scss": "2025-07-11T09:20:23.5185992-07:00", "src/app/shared/components/notes/notes.component.ts": "2025-07-11T09:20:23.5624974-07:00", "src/app/shared/components/page-title/page-title.component.scss": "2025-07-11T09:20:23.618681-07:00", "src/app/shared/components/page-title/page-title.component.ts": "2025-07-11T09:20:23.6600872-07:00", "src/app/shared/shared.module.ts": "2025-07-11T09:20:20.4132374-07:00", "src/assets/CSVs/MRSDS_Template_CQ(Template).csv": "2025-07-11T09:20:23.9645828-07:00", "src/assets/fonts/urbane/Urbane-Bold.eot": "2025-07-11T09:20:24.1684487-07:00", "src/assets/fonts/urbane/Urbane-Bold.ttf": "2025-07-11T09:20:24.2275142-07:00", "src/assets/fonts/urbane/Urbane-Bold.woff": "2025-07-11T09:20:24.2764137-07:00", "src/assets/fonts/urbane/Urbane-Bold.woff2": "2025-07-11T09:20:24.3212896-07:00", "src/assets/fonts/urbane/Urbane-BoldItalic.eot": "2025-07-11T09:20:24.366104-07:00", "src/assets/fonts/urbane/Urbane-BoldItalic.ttf": "2025-07-11T09:20:24.4093641-07:00", "src/assets/fonts/urbane/Urbane-BoldItalic.woff": "2025-07-11T09:20:24.4476108-07:00", "src/assets/fonts/urbane/Urbane-BoldItalic.woff2": "2025-07-11T09:20:24.4808853-07:00", "src/assets/fonts/urbane/Urbane-DemiBold.eot": "2025-07-11T09:20:24.5141405-07:00", "src/assets/fonts/urbane/Urbane-DemiBold.ttf": "2025-07-11T09:20:24.5547357-07:00", "src/assets/fonts/urbane/Urbane-DemiBold.woff": "2025-07-11T09:20:24.5907316-07:00", "src/assets/fonts/urbane/Urbane-DemiBold.woff2": "2025-07-11T09:20:24.6288328-07:00", "src/assets/fonts/urbane/Urbane-DemiBoldItalic.eot": "2025-07-11T09:20:24.674465-07:00", "src/assets/fonts/urbane/Urbane-DemiBoldItalic.ttf": "2025-07-11T09:20:24.7267478-07:00", "src/assets/fonts/urbane/Urbane-DemiBoldItalic.woff": "2025-07-11T09:20:24.7599775-07:00", "src/assets/fonts/urbane/Urbane-DemiBoldItalic.woff2": "2025-07-11T09:20:24.7949777-07:00", "src/assets/fonts/urbane/Urbane-ExtraLight.eot": "2025-07-11T09:20:24.8293914-07:00", "src/assets/fonts/urbane/Urbane-ExtraLight.ttf": "2025-07-11T09:20:24.8724881-07:00", "src/assets/fonts/urbane/Urbane-ExtraLight.woff": "2025-07-11T09:20:24.907488-07:00", "src/assets/fonts/urbane/Urbane-ExtraLight.woff2": "2025-07-11T09:20:24.9418218-07:00", "src/assets/fonts/urbane/Urbane-ExtraLightItalic.eot": "2025-07-11T09:20:24.9777259-07:00", "src/assets/fonts/urbane/Urbane-ExtraLightItalic.ttf": "2025-07-11T09:20:25.0210645-07:00", "src/assets/fonts/urbane/Urbane-ExtraLightItalic.woff": "2025-07-11T09:20:25.0580644-07:00", "src/assets/fonts/urbane/Urbane-ExtraLightItalic.woff2": "2025-07-11T09:20:25.1006641-07:00", "src/assets/fonts/urbane/Urbane-Heavy.eot": "2025-07-11T09:20:25.1415309-07:00", "src/assets/fonts/urbane/Urbane-Heavy.ttf": "2025-07-11T09:20:25.2011636-07:00", "src/assets/fonts/urbane/Urbane-Heavy.woff": "2025-07-11T09:20:25.2647351-07:00", "src/assets/fonts/urbane/Urbane-Heavy.woff2": "2025-07-11T09:20:25.3321427-07:00", "src/assets/fonts/urbane/Urbane-HeavyItalic.eot": "2025-07-11T09:20:25.3963844-07:00", "src/assets/fonts/urbane/Urbane-HeavyItalic.ttf": "2025-07-11T09:20:25.4610317-07:00", "src/assets/fonts/urbane/Urbane-HeavyItalic.woff": "2025-07-11T09:20:25.512697-07:00", "src/assets/fonts/urbane/Urbane-HeavyItalic.woff2": "2025-07-11T09:20:25.5623339-07:00", "src/assets/fonts/urbane/Urbane-Light.eot": "2025-07-11T09:20:25.6169884-07:00", "src/assets/fonts/urbane/Urbane-Light.ttf": "2025-07-11T09:20:25.6692395-07:00", "src/assets/fonts/urbane/Urbane-Light.woff": "2025-07-11T09:20:25.7104505-07:00", "src/assets/fonts/urbane/Urbane-Light.woff2": "2025-07-11T09:20:25.7606324-07:00", "src/assets/fonts/urbane/Urbane-LightItalic.eot": "2025-07-11T09:20:25.8043563-07:00", "src/assets/fonts/urbane/Urbane-LightItalic.ttf": "2025-07-11T09:20:25.8647392-07:00", "src/assets/fonts/urbane/Urbane-LightItalic.woff": "2025-07-11T09:20:25.9094146-07:00", "src/assets/fonts/urbane/Urbane-LightItalic.woff2": "2025-07-11T09:20:25.9570295-07:00", "src/assets/fonts/urbane/Urbane-Medium.eot": "2025-07-11T09:20:26.0024933-07:00", "src/assets/fonts/urbane/Urbane-Medium.ttf": "2025-07-11T09:20:26.0559994-07:00", "src/assets/fonts/urbane/Urbane-Medium.woff": "2025-07-11T09:20:26.0986159-07:00", "src/assets/fonts/urbane/Urbane-Medium.woff2": "2025-07-11T09:20:26.1604727-07:00", "src/assets/fonts/urbane/Urbane-MediumItalic.eot": "2025-07-11T09:20:26.2218542-07:00", "src/assets/fonts/urbane/Urbane-MediumItalic.ttf": "2025-07-11T09:20:26.279174-07:00", "src/assets/fonts/urbane/Urbane-MediumItalic.woff": "2025-07-11T09:20:26.3121823-07:00", "src/assets/fonts/urbane/Urbane-MediumItalic.woff2": "2025-07-11T09:20:26.3491191-07:00", "src/assets/fonts/urbane/Urbane-Thin.eot": "2025-07-11T09:20:26.3871185-07:00", "src/assets/fonts/urbane/Urbane-Thin.ttf": "2025-07-11T09:20:26.4311178-07:00", "src/assets/fonts/urbane/Urbane-Thin.woff": "2025-07-11T09:20:26.4641509-07:00", "src/assets/fonts/urbane/Urbane-Thin.woff2": "2025-07-11T09:20:26.496904-07:00", "src/assets/fonts/urbane/Urbane-ThinItalic.eot": "2025-07-11T09:20:26.5413073-07:00", "src/assets/fonts/urbane/Urbane-ThinItalic.ttf": "2025-07-11T09:20:26.5899625-07:00", "src/assets/fonts/urbane/Urbane-ThinItalic.woff": "2025-07-11T09:20:26.6308666-07:00", "src/assets/fonts/urbane/Urbane-ThinItalic.woff2": "2025-07-11T09:20:26.672749-07:00", "src/assets/fonts/urbane/demo.html": "2025-07-11T09:20:24.0704514-07:00", "src/assets/fonts/urbane/stylesheet.css": "2025-07-11T09:20:24.1245475-07:00", "src/assets/logos/Stellarus-Favicon-red.png": "2025-07-11T09:20:26.7526062-07:00", "src/assets/logos/Stellarus_logo_2C_blacktype.png": "2025-07-11T09:20:26.8030302-07:00", "src/assets/pdf.worker.mjs": "2025-07-11T09:16:56.3317556-07:00", "src/environments/environment.prod.ts": "2025-07-14T12:50:54.7793017-07:00", "src/environments/environment.ts": "2025-08-13T10:48:00.8810083-07:00", "src/index.html": "2025-07-11T09:20:16.2924945-07:00", "src/main.server.ts": "2025-07-11T09:20:16.3352619-07:00", "src/main.ts": "2025-07-11T09:20:16.373227-07:00", "src/server.ts": "2025-08-14T11:15:22.7582938-07:00", "src/styles.scss": "2025-07-11T09:20:16.4498152-07:00", "src/styles/_mixins.scss": "2025-07-11T09:20:26.8640303-07:00", "src/styles/_theme.scss": "2025-07-11T09:20:26.9051633-07:00", "src/styles/_typography.scss": "2025-07-11T09:20:26.9453978-07:00", "src/styles/_variables.scss": "2025-07-11T09:20:26.9869827-07:00", "structure.txt": "2025-07-11T09:21:52.9062971-07:00", "tsconfig.app.json": "2025-07-11T09:21:52.9413536-07:00", "tsconfig.json": "2025-07-11T09:21:52.9703155-07:00", "tsconfig.spec.json": "2025-07-11T09:21:53.0023442-07:00"}, "local_to_remote_names": {".editorconfig": ".editorconfig", ".gitignore": ".giti<PERSON>re", "DATABRICKS_INTEGRATION.md": "DATABRICKS_INTEGRATION.md", "README.md": "README.md", "angular.json": "angular.json", "app.yaml": "app.yaml", "azure-quick-deploy.md": "azure-quick-deploy.md", "copy-pdf-worker.js": "copy-pdf-worker.js", "databricks-deployment-alternative.md": "databricks-deployment-alternative.md", "deployment-guide.md": "deployment-guide.md", "package.json": "package.json", "public/favicon.ico": "public/favicon.ico", "src/app/app.component.html": "src/app/app.component.html", "src/app/app.component.scss": "src/app/app.component.scss", "src/app/app.component.spec.ts": "src/app/app.component.spec.ts", "src/app/app.component.ts": "src/app/app.component.ts", "src/app/app.config.server.ts": "src/app/app.config.server.ts", "src/app/app.config.ts": "src/app/app.config.ts", "src/app/app.routes.ts": "src/app/app.routes.ts", "src/app/core/config/databricks.config.ts": "src/app/core/config/databricks.config.ts", "src/app/core/core.module.ts": "src/app/core/core.module.ts", "src/app/core/data/data.module.ts": "src/app/core/data/data.module.ts", "src/app/core/data/models/chart-data.models.ts": "src/app/core/data/models/chart-data.models.ts", "src/app/core/data/services/csv-data.service.ts": "src/app/core/data/services/csv-data.service.ts", "src/app/core/data/services/storage.service.spec.ts": "src/app/core/data/services/storage.service.spec.ts", "src/app/core/data/services/storage.service.ts": "src/app/core/data/services/storage.service.ts", "src/app/core/services/databricks.service.ts": "src/app/core/services/databricks.service.ts", "src/app/features/auth/auth-routing.module.ts": "src/app/features/auth/auth-routing.module.ts", "src/app/features/auth/auth.module.ts": "src/app/features/auth/auth.module.ts", "src/app/features/auth/components/login/login.component.html": "src/app/features/auth/components/login/login.component.html", "src/app/features/auth/components/login/login.component.scss": "src/app/features/auth/components/login/login.component.scss", "src/app/features/auth/components/login/login.component.spec.ts": "src/app/features/auth/components/login/login.component.spec.ts", "src/app/features/auth/components/login/login.component.ts": "src/app/features/auth/components/login/login.component.ts", "src/app/features/auth/pages/login-page/login-page.component.html": "src/app/features/auth/pages/login-page/login-page.component.html", "src/app/features/auth/pages/login-page/login-page.component.scss": "src/app/features/auth/pages/login-page/login-page.component.scss", "src/app/features/auth/pages/login-page/login-page.component.spec.ts": "src/app/features/auth/pages/login-page/login-page.component.spec.ts", "src/app/features/auth/pages/login-page/login-page.component.ts": "src/app/features/auth/pages/login-page/login-page.component.ts", "src/app/features/auth/services/auth.service.spec.ts": "src/app/features/auth/services/auth.service.spec.ts", "src/app/features/auth/services/auth.service.ts": "src/app/features/auth/services/auth.service.ts", "src/app/features/chart-review/chart-review-routing.module.ts": "src/app/features/chart-review/chart-review-routing.module.ts", "src/app/features/chart-review/chart-review.module.ts": "src/app/features/chart-review/chart-review.module.ts", "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.html": "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.html", "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.scss": "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.scss", "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.ts": "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.ts", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.html": "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.html", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.scss": "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.scss", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.spec.ts": "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.spec.ts", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.ts": "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.ts", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.html": "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.html", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.scss": "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.scss", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.spec.ts": "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.spec.ts", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.ts": "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.ts", "src/app/features/chart-review/services/annotation.service.spec.ts": "src/app/features/chart-review/services/annotation.service.spec.ts", "src/app/features/chart-review/services/annotation.service.ts": "src/app/features/chart-review/services/annotation.service.ts", "src/app/features/chart-review/services/pdf.service.spec.ts": "src/app/features/chart-review/services/pdf.service.spec.ts", "src/app/features/chart-review/services/pdf.service.ts": "src/app/features/chart-review/services/pdf.service.ts", "src/app/features/dashboard/components/assigned-table/assigned-table.component.html": "src/app/features/dashboard/components/assigned-table/assigned-table.component.html", "src/app/features/dashboard/components/assigned-table/assigned-table.component.scss": "src/app/features/dashboard/components/assigned-table/assigned-table.component.scss", "src/app/features/dashboard/components/assigned-table/assigned-table.component.ts": "src/app/features/dashboard/components/assigned-table/assigned-table.component.ts", "src/app/features/dashboard/components/chart-list/chart-list.component.html": "src/app/features/dashboard/components/chart-list/chart-list.component.html", "src/app/features/dashboard/components/chart-list/chart-list.component.scss": "src/app/features/dashboard/components/chart-list/chart-list.component.scss", "src/app/features/dashboard/components/chart-list/chart-list.component.ts": "src/app/features/dashboard/components/chart-list/chart-list.component.ts", "src/app/features/dashboard/components/filter-button/filter-button.component.html": "src/app/features/dashboard/components/filter-button/filter-button.component.html", "src/app/features/dashboard/components/filter-button/filter-button.component.scss": "src/app/features/dashboard/components/filter-button/filter-button.component.scss", "src/app/features/dashboard/components/filter-button/filter-button.component.ts": "src/app/features/dashboard/components/filter-button/filter-button.component.ts", "src/app/features/dashboard/components/measure-summary/measure-summary.component.html": "src/app/features/dashboard/components/measure-summary/measure-summary.component.html", "src/app/features/dashboard/components/measure-summary/measure-summary.component.scss": "src/app/features/dashboard/components/measure-summary/measure-summary.component.scss", "src/app/features/dashboard/components/measure-summary/measure-summary.component.spec.ts": "src/app/features/dashboard/components/measure-summary/measure-summary.component.spec.ts", "src/app/features/dashboard/components/measure-summary/measure-summary.component.ts": "src/app/features/dashboard/components/measure-summary/measure-summary.component.ts", "src/app/features/dashboard/components/pagination/pagination.component.html": "src/app/features/dashboard/components/pagination/pagination.component.html", "src/app/features/dashboard/components/pagination/pagination.component.scss": "src/app/features/dashboard/components/pagination/pagination.component.scss", "src/app/features/dashboard/components/pagination/pagination.component.ts": "src/app/features/dashboard/components/pagination/pagination.component.ts", "src/app/features/dashboard/components/search-filter/search-filter.component.html": "src/app/features/dashboard/components/search-filter/search-filter.component.html", "src/app/features/dashboard/components/search-filter/search-filter.component.scss": "src/app/features/dashboard/components/search-filter/search-filter.component.scss", "src/app/features/dashboard/components/search-filter/search-filter.component.ts": "src/app/features/dashboard/components/search-filter/search-filter.component.ts", "src/app/features/dashboard/components/sort-button/sort-button.component.html": "src/app/features/dashboard/components/sort-button/sort-button.component.html", "src/app/features/dashboard/components/sort-button/sort-button.component.scss": "src/app/features/dashboard/components/sort-button/sort-button.component.scss", "src/app/features/dashboard/components/sort-button/sort-button.component.ts": "src/app/features/dashboard/components/sort-button/sort-button.component.ts", "src/app/features/dashboard/dashboard-routing.module.ts": "src/app/features/dashboard/dashboard-routing.module.ts", "src/app/features/dashboard/dashboard.module.ts": "src/app/features/dashboard/dashboard.module.ts", "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.html": "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.html", "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.scss": "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.scss", "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.spec.ts": "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.spec.ts", "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.ts": "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.ts", "src/app/shared/components/ai-highlights/ai-highlights.component.html": "src/app/shared/components/ai-highlights/ai-highlights.component.html", "src/app/shared/components/ai-highlights/ai-highlights.component.scss": "src/app/shared/components/ai-highlights/ai-highlights.component.scss", "src/app/shared/components/ai-highlights/ai-highlights.component.ts": "src/app/shared/components/ai-highlights/ai-highlights.component.ts", "src/app/shared/components/buttons/button.component.html": "src/app/shared/components/buttons/button.component.html", "src/app/shared/components/buttons/button.component.scss": "src/app/shared/components/buttons/button.component.scss", "src/app/shared/components/buttons/button.component.ts": "src/app/shared/components/buttons/button.component.ts", "src/app/shared/components/component-test/component-test.component.html": "src/app/shared/components/component-test/component-test.component.html", "src/app/shared/components/component-test/component-test.component.scss": "src/app/shared/components/component-test/component-test.component.scss", "src/app/shared/components/component-test/component-test.component.ts": "src/app/shared/components/component-test/component-test.component.ts", "src/app/shared/components/demographics/demographics.component.html": "src/app/shared/components/demographics/demographics.component.html", "src/app/shared/components/demographics/demographics.component.scss": "src/app/shared/components/demographics/demographics.component.scss", "src/app/shared/components/demographics/demographics.component.ts": "src/app/shared/components/demographics/demographics.component.ts", "src/app/shared/components/form-controls/calendar/calendar.component.html": "src/app/shared/components/form-controls/calendar/calendar.component.html", "src/app/shared/components/form-controls/calendar/calendar.component.scss": "src/app/shared/components/form-controls/calendar/calendar.component.scss", "src/app/shared/components/form-controls/calendar/calendar.component.ts": "src/app/shared/components/form-controls/calendar/calendar.component.ts", "src/app/shared/components/form-controls/checkbox/checkbox.component.html": "src/app/shared/components/form-controls/checkbox/checkbox.component.html", "src/app/shared/components/form-controls/checkbox/checkbox.component.scss": "src/app/shared/components/form-controls/checkbox/checkbox.component.scss", "src/app/shared/components/form-controls/checkbox/checkbox.component.ts": "src/app/shared/components/form-controls/checkbox/checkbox.component.ts", "src/app/shared/components/form-controls/comment-box/comment-box.component.html": "src/app/shared/components/form-controls/comment-box/comment-box.component.html", "src/app/shared/components/form-controls/comment-box/comment-box.component.scss": "src/app/shared/components/form-controls/comment-box/comment-box.component.scss", "src/app/shared/components/form-controls/comment-box/comment-box.component.ts": "src/app/shared/components/form-controls/comment-box/comment-box.component.ts", "src/app/shared/components/form-controls/dropdown/dropdown.component.html": "src/app/shared/components/form-controls/dropdown/dropdown.component.html", "src/app/shared/components/form-controls/dropdown/dropdown.component.scss": "src/app/shared/components/form-controls/dropdown/dropdown.component.scss", "src/app/shared/components/form-controls/dropdown/dropdown.component.ts": "src/app/shared/components/form-controls/dropdown/dropdown.component.ts", "src/app/shared/components/form-controls/reasoning-dropdown/reasoning-dropdown.component.ts": "src/app/shared/components/form-controls/reasoning-dropdown/reasoning-dropdown.component.ts", "src/app/shared/components/form-controls/results/results.component.html": "src/app/shared/components/form-controls/results/results.component.html", "src/app/shared/components/form-controls/results/results.component.scss": "src/app/shared/components/form-controls/results/results.component.scss", "src/app/shared/components/form-controls/results/results.component.ts": "src/app/shared/components/form-controls/results/results.component.ts", "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.html": "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.html", "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.scss": "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.scss", "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.ts": "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.ts", "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.html": "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.html", "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.scss": "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.scss", "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.ts": "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.ts", "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.html": "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.html", "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.scss": "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.scss", "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.ts": "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.ts", "src/app/shared/components/form-controls/value-input/value-input.component.html": "src/app/shared/components/form-controls/value-input/value-input.component.html", "src/app/shared/components/form-controls/value-input/value-input.component.scss": "src/app/shared/components/form-controls/value-input/value-input.component.scss", "src/app/shared/components/form-controls/value-input/value-input.component.ts": "src/app/shared/components/form-controls/value-input/value-input.component.ts", "src/app/shared/components/header/header.component.html": "src/app/shared/components/header/header.component.html", "src/app/shared/components/header/header.component.scss": "src/app/shared/components/header/header.component.scss", "src/app/shared/components/header/header.component.ts": "src/app/shared/components/header/header.component.ts", "src/app/shared/components/hits/hits.component.html": "src/app/shared/components/hits/hits.component.html", "src/app/shared/components/hits/hits.component.scss": "src/app/shared/components/hits/hits.component.scss", "src/app/shared/components/hits/hits.component.ts": "src/app/shared/components/hits/hits.component.ts", "src/app/shared/components/icons/icon.component.html": "src/app/shared/components/icons/icon.component.html", "src/app/shared/components/icons/icon.component.scss": "src/app/shared/components/icons/icon.component.scss", "src/app/shared/components/icons/icon.component.ts": "src/app/shared/components/icons/icon.component.ts", "src/app/shared/components/icons/refresh-icon.component.scss": "src/app/shared/components/icons/refresh-icon.component.scss", "src/app/shared/components/icons/refresh-icon.component.ts": "src/app/shared/components/icons/refresh-icon.component.ts", "src/app/shared/components/menu/menu.component.html": "src/app/shared/components/menu/menu.component.html", "src/app/shared/components/menu/menu.component.scss": "src/app/shared/components/menu/menu.component.scss", "src/app/shared/components/menu/menu.component.ts": "src/app/shared/components/menu/menu.component.ts", "src/app/shared/components/notes/notes.component.html": "src/app/shared/components/notes/notes.component.html", "src/app/shared/components/notes/notes.component.scss": "src/app/shared/components/notes/notes.component.scss", "src/app/shared/components/notes/notes.component.ts": "src/app/shared/components/notes/notes.component.ts", "src/app/shared/components/page-title/page-title.component.scss": "src/app/shared/components/page-title/page-title.component.scss", "src/app/shared/components/page-title/page-title.component.ts": "src/app/shared/components/page-title/page-title.component.ts", "src/app/shared/shared.module.ts": "src/app/shared/shared.module.ts", "src/assets/CSVs/MRSDS_Template_CQ(Template).csv": "src/assets/CSVs/MRSDS_Template_CQ(Template).csv", "src/assets/fonts/urbane/Urbane-Bold.eot": "src/assets/fonts/urbane/Urbane-Bold.eot", "src/assets/fonts/urbane/Urbane-Bold.ttf": "src/assets/fonts/urbane/Urbane-Bold.ttf", "src/assets/fonts/urbane/Urbane-Bold.woff": "src/assets/fonts/urbane/Urbane-Bold.woff", "src/assets/fonts/urbane/Urbane-Bold.woff2": "src/assets/fonts/urbane/Urbane-Bold.woff2", "src/assets/fonts/urbane/Urbane-BoldItalic.eot": "src/assets/fonts/urbane/Urbane-BoldItalic.eot", "src/assets/fonts/urbane/Urbane-BoldItalic.ttf": "src/assets/fonts/urbane/Urbane-BoldItalic.ttf", "src/assets/fonts/urbane/Urbane-BoldItalic.woff": "src/assets/fonts/urbane/Urbane-BoldItalic.woff", "src/assets/fonts/urbane/Urbane-BoldItalic.woff2": "src/assets/fonts/urbane/Urbane-BoldItalic.woff2", "src/assets/fonts/urbane/Urbane-DemiBold.eot": "src/assets/fonts/urbane/Urbane-DemiBold.eot", "src/assets/fonts/urbane/Urbane-DemiBold.ttf": "src/assets/fonts/urbane/Urbane-DemiBold.ttf", "src/assets/fonts/urbane/Urbane-DemiBold.woff": "src/assets/fonts/urbane/Urbane-DemiBold.woff", "src/assets/fonts/urbane/Urbane-DemiBold.woff2": "src/assets/fonts/urbane/Urbane-DemiBold.woff2", "src/assets/fonts/urbane/Urbane-DemiBoldItalic.eot": "src/assets/fonts/urbane/Urbane-DemiBoldItalic.eot", "src/assets/fonts/urbane/Urbane-DemiBoldItalic.ttf": "src/assets/fonts/urbane/Urbane-DemiBoldItalic.ttf", "src/assets/fonts/urbane/Urbane-DemiBoldItalic.woff": "src/assets/fonts/urbane/Urbane-DemiBoldItalic.woff", "src/assets/fonts/urbane/Urbane-DemiBoldItalic.woff2": "src/assets/fonts/urbane/Urbane-DemiBoldItalic.woff2", "src/assets/fonts/urbane/Urbane-ExtraLight.eot": "src/assets/fonts/urbane/Urbane-ExtraLight.eot", "src/assets/fonts/urbane/Urbane-ExtraLight.ttf": "src/assets/fonts/urbane/Urbane-ExtraLight.ttf", "src/assets/fonts/urbane/Urbane-ExtraLight.woff": "src/assets/fonts/urbane/Urbane-ExtraLight.woff", "src/assets/fonts/urbane/Urbane-ExtraLight.woff2": "src/assets/fonts/urbane/Urbane-ExtraLight.woff2", "src/assets/fonts/urbane/Urbane-ExtraLightItalic.eot": "src/assets/fonts/urbane/Urbane-ExtraLightItalic.eot", "src/assets/fonts/urbane/Urbane-ExtraLightItalic.ttf": "src/assets/fonts/urbane/Urbane-ExtraLightItalic.ttf", "src/assets/fonts/urbane/Urbane-ExtraLightItalic.woff": "src/assets/fonts/urbane/Urbane-ExtraLightItalic.woff", "src/assets/fonts/urbane/Urbane-ExtraLightItalic.woff2": "src/assets/fonts/urbane/Urbane-ExtraLightItalic.woff2", "src/assets/fonts/urbane/Urbane-Heavy.eot": "src/assets/fonts/urbane/Urbane-Heavy.eot", "src/assets/fonts/urbane/Urbane-Heavy.ttf": "src/assets/fonts/urbane/Urbane-Heavy.ttf", "src/assets/fonts/urbane/Urbane-Heavy.woff": "src/assets/fonts/urbane/Urbane-Heavy.woff", "src/assets/fonts/urbane/Urbane-Heavy.woff2": "src/assets/fonts/urbane/Urbane-Heavy.woff2", "src/assets/fonts/urbane/Urbane-HeavyItalic.eot": "src/assets/fonts/urbane/Urbane-HeavyItalic.eot", "src/assets/fonts/urbane/Urbane-HeavyItalic.ttf": "src/assets/fonts/urbane/Urbane-HeavyItalic.ttf", "src/assets/fonts/urbane/Urbane-HeavyItalic.woff": "src/assets/fonts/urbane/Urbane-HeavyItalic.woff", "src/assets/fonts/urbane/Urbane-HeavyItalic.woff2": "src/assets/fonts/urbane/Urbane-HeavyItalic.woff2", "src/assets/fonts/urbane/Urbane-Light.eot": "src/assets/fonts/urbane/Urbane-Light.eot", "src/assets/fonts/urbane/Urbane-Light.ttf": "src/assets/fonts/urbane/Urbane-Light.ttf", "src/assets/fonts/urbane/Urbane-Light.woff": "src/assets/fonts/urbane/Urbane-Light.woff", "src/assets/fonts/urbane/Urbane-Light.woff2": "src/assets/fonts/urbane/Urbane-Light.woff2", "src/assets/fonts/urbane/Urbane-LightItalic.eot": "src/assets/fonts/urbane/Urbane-LightItalic.eot", "src/assets/fonts/urbane/Urbane-LightItalic.ttf": "src/assets/fonts/urbane/Urbane-LightItalic.ttf", "src/assets/fonts/urbane/Urbane-LightItalic.woff": "src/assets/fonts/urbane/Urbane-LightItalic.woff", "src/assets/fonts/urbane/Urbane-LightItalic.woff2": "src/assets/fonts/urbane/Urbane-LightItalic.woff2", "src/assets/fonts/urbane/Urbane-Medium.eot": "src/assets/fonts/urbane/Urbane-Medium.eot", "src/assets/fonts/urbane/Urbane-Medium.ttf": "src/assets/fonts/urbane/Urbane-Medium.ttf", "src/assets/fonts/urbane/Urbane-Medium.woff": "src/assets/fonts/urbane/Urbane-Medium.woff", "src/assets/fonts/urbane/Urbane-Medium.woff2": "src/assets/fonts/urbane/Urbane-Medium.woff2", "src/assets/fonts/urbane/Urbane-MediumItalic.eot": "src/assets/fonts/urbane/Urbane-MediumItalic.eot", "src/assets/fonts/urbane/Urbane-MediumItalic.ttf": "src/assets/fonts/urbane/Urbane-MediumItalic.ttf", "src/assets/fonts/urbane/Urbane-MediumItalic.woff": "src/assets/fonts/urbane/Urbane-MediumItalic.woff", "src/assets/fonts/urbane/Urbane-MediumItalic.woff2": "src/assets/fonts/urbane/Urbane-MediumItalic.woff2", "src/assets/fonts/urbane/Urbane-Thin.eot": "src/assets/fonts/urbane/Urbane-Thin.eot", "src/assets/fonts/urbane/Urbane-Thin.ttf": "src/assets/fonts/urbane/Urbane-Thin.ttf", "src/assets/fonts/urbane/Urbane-Thin.woff": "src/assets/fonts/urbane/Urbane-Thin.woff", "src/assets/fonts/urbane/Urbane-Thin.woff2": "src/assets/fonts/urbane/Urbane-Thin.woff2", "src/assets/fonts/urbane/Urbane-ThinItalic.eot": "src/assets/fonts/urbane/Urbane-ThinItalic.eot", "src/assets/fonts/urbane/Urbane-ThinItalic.ttf": "src/assets/fonts/urbane/Urbane-ThinItalic.ttf", "src/assets/fonts/urbane/Urbane-ThinItalic.woff": "src/assets/fonts/urbane/Urbane-ThinItalic.woff", "src/assets/fonts/urbane/Urbane-ThinItalic.woff2": "src/assets/fonts/urbane/Urbane-ThinItalic.woff2", "src/assets/fonts/urbane/demo.html": "src/assets/fonts/urbane/demo.html", "src/assets/fonts/urbane/stylesheet.css": "src/assets/fonts/urbane/stylesheet.css", "src/assets/logos/Stellarus-Favicon-red.png": "src/assets/logos/Stellarus-Favicon-red.png", "src/assets/logos/Stellarus_logo_2C_blacktype.png": "src/assets/logos/Stellarus_logo_2C_blacktype.png", "src/assets/pdf.worker.mjs": "src/assets/pdf.worker.mjs", "src/environments/environment.prod.ts": "src/environments/environment.prod.ts", "src/environments/environment.ts": "src/environments/environment.ts", "src/index.html": "src/index.html", "src/main.server.ts": "src/main.server.ts", "src/main.ts": "src/main.ts", "src/server.ts": "src/server.ts", "src/styles.scss": "src/styles.scss", "src/styles/_mixins.scss": "src/styles/_mixins.scss", "src/styles/_theme.scss": "src/styles/_theme.scss", "src/styles/_typography.scss": "src/styles/_typography.scss", "src/styles/_variables.scss": "src/styles/_variables.scss", "structure.txt": "structure.txt", "tsconfig.app.json": "tsconfig.app.json", "tsconfig.json": "tsconfig.json", "tsconfig.spec.json": "tsconfig.spec.json"}, "remote_to_local_names": {".editorconfig": ".editorconfig", ".gitignore": ".giti<PERSON>re", "DATABRICKS_INTEGRATION.md": "DATABRICKS_INTEGRATION.md", "README.md": "README.md", "angular.json": "angular.json", "app.yaml": "app.yaml", "azure-quick-deploy.md": "azure-quick-deploy.md", "copy-pdf-worker.js": "copy-pdf-worker.js", "databricks-deployment-alternative.md": "databricks-deployment-alternative.md", "deployment-guide.md": "deployment-guide.md", "package.json": "package.json", "public/favicon.ico": "public/favicon.ico", "src/app/app.component.html": "src/app/app.component.html", "src/app/app.component.scss": "src/app/app.component.scss", "src/app/app.component.spec.ts": "src/app/app.component.spec.ts", "src/app/app.component.ts": "src/app/app.component.ts", "src/app/app.config.server.ts": "src/app/app.config.server.ts", "src/app/app.config.ts": "src/app/app.config.ts", "src/app/app.routes.ts": "src/app/app.routes.ts", "src/app/core/config/databricks.config.ts": "src/app/core/config/databricks.config.ts", "src/app/core/core.module.ts": "src/app/core/core.module.ts", "src/app/core/data/data.module.ts": "src/app/core/data/data.module.ts", "src/app/core/data/models/chart-data.models.ts": "src/app/core/data/models/chart-data.models.ts", "src/app/core/data/services/csv-data.service.ts": "src/app/core/data/services/csv-data.service.ts", "src/app/core/data/services/storage.service.spec.ts": "src/app/core/data/services/storage.service.spec.ts", "src/app/core/data/services/storage.service.ts": "src/app/core/data/services/storage.service.ts", "src/app/core/services/databricks.service.ts": "src/app/core/services/databricks.service.ts", "src/app/features/auth/auth-routing.module.ts": "src/app/features/auth/auth-routing.module.ts", "src/app/features/auth/auth.module.ts": "src/app/features/auth/auth.module.ts", "src/app/features/auth/components/login/login.component.html": "src/app/features/auth/components/login/login.component.html", "src/app/features/auth/components/login/login.component.scss": "src/app/features/auth/components/login/login.component.scss", "src/app/features/auth/components/login/login.component.spec.ts": "src/app/features/auth/components/login/login.component.spec.ts", "src/app/features/auth/components/login/login.component.ts": "src/app/features/auth/components/login/login.component.ts", "src/app/features/auth/pages/login-page/login-page.component.html": "src/app/features/auth/pages/login-page/login-page.component.html", "src/app/features/auth/pages/login-page/login-page.component.scss": "src/app/features/auth/pages/login-page/login-page.component.scss", "src/app/features/auth/pages/login-page/login-page.component.spec.ts": "src/app/features/auth/pages/login-page/login-page.component.spec.ts", "src/app/features/auth/pages/login-page/login-page.component.ts": "src/app/features/auth/pages/login-page/login-page.component.ts", "src/app/features/auth/services/auth.service.spec.ts": "src/app/features/auth/services/auth.service.spec.ts", "src/app/features/auth/services/auth.service.ts": "src/app/features/auth/services/auth.service.ts", "src/app/features/chart-review/chart-review-routing.module.ts": "src/app/features/chart-review/chart-review-routing.module.ts", "src/app/features/chart-review/chart-review.module.ts": "src/app/features/chart-review/chart-review.module.ts", "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.html": "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.html", "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.scss": "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.scss", "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.ts": "src/app/features/chart-review/components/pdf-viewer-test/pdf-viewer-test.component.ts", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.html": "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.html", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.scss": "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.scss", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.spec.ts": "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.spec.ts", "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.ts": "src/app/features/chart-review/components/pdf-viewer/pdf-viewer.component.ts", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.html": "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.html", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.scss": "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.scss", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.spec.ts": "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.spec.ts", "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.ts": "src/app/features/chart-review/pages/chart-review-page/chart-review-page.component.ts", "src/app/features/chart-review/services/annotation.service.spec.ts": "src/app/features/chart-review/services/annotation.service.spec.ts", "src/app/features/chart-review/services/annotation.service.ts": "src/app/features/chart-review/services/annotation.service.ts", "src/app/features/chart-review/services/pdf.service.spec.ts": "src/app/features/chart-review/services/pdf.service.spec.ts", "src/app/features/chart-review/services/pdf.service.ts": "src/app/features/chart-review/services/pdf.service.ts", "src/app/features/dashboard/components/assigned-table/assigned-table.component.html": "src/app/features/dashboard/components/assigned-table/assigned-table.component.html", "src/app/features/dashboard/components/assigned-table/assigned-table.component.scss": "src/app/features/dashboard/components/assigned-table/assigned-table.component.scss", "src/app/features/dashboard/components/assigned-table/assigned-table.component.ts": "src/app/features/dashboard/components/assigned-table/assigned-table.component.ts", "src/app/features/dashboard/components/chart-list/chart-list.component.html": "src/app/features/dashboard/components/chart-list/chart-list.component.html", "src/app/features/dashboard/components/chart-list/chart-list.component.scss": "src/app/features/dashboard/components/chart-list/chart-list.component.scss", "src/app/features/dashboard/components/chart-list/chart-list.component.ts": "src/app/features/dashboard/components/chart-list/chart-list.component.ts", "src/app/features/dashboard/components/filter-button/filter-button.component.html": "src/app/features/dashboard/components/filter-button/filter-button.component.html", "src/app/features/dashboard/components/filter-button/filter-button.component.scss": "src/app/features/dashboard/components/filter-button/filter-button.component.scss", "src/app/features/dashboard/components/filter-button/filter-button.component.ts": "src/app/features/dashboard/components/filter-button/filter-button.component.ts", "src/app/features/dashboard/components/measure-summary/measure-summary.component.html": "src/app/features/dashboard/components/measure-summary/measure-summary.component.html", "src/app/features/dashboard/components/measure-summary/measure-summary.component.scss": "src/app/features/dashboard/components/measure-summary/measure-summary.component.scss", "src/app/features/dashboard/components/measure-summary/measure-summary.component.spec.ts": "src/app/features/dashboard/components/measure-summary/measure-summary.component.spec.ts", "src/app/features/dashboard/components/measure-summary/measure-summary.component.ts": "src/app/features/dashboard/components/measure-summary/measure-summary.component.ts", "src/app/features/dashboard/components/pagination/pagination.component.html": "src/app/features/dashboard/components/pagination/pagination.component.html", "src/app/features/dashboard/components/pagination/pagination.component.scss": "src/app/features/dashboard/components/pagination/pagination.component.scss", "src/app/features/dashboard/components/pagination/pagination.component.ts": "src/app/features/dashboard/components/pagination/pagination.component.ts", "src/app/features/dashboard/components/search-filter/search-filter.component.html": "src/app/features/dashboard/components/search-filter/search-filter.component.html", "src/app/features/dashboard/components/search-filter/search-filter.component.scss": "src/app/features/dashboard/components/search-filter/search-filter.component.scss", "src/app/features/dashboard/components/search-filter/search-filter.component.ts": "src/app/features/dashboard/components/search-filter/search-filter.component.ts", "src/app/features/dashboard/components/sort-button/sort-button.component.html": "src/app/features/dashboard/components/sort-button/sort-button.component.html", "src/app/features/dashboard/components/sort-button/sort-button.component.scss": "src/app/features/dashboard/components/sort-button/sort-button.component.scss", "src/app/features/dashboard/components/sort-button/sort-button.component.ts": "src/app/features/dashboard/components/sort-button/sort-button.component.ts", "src/app/features/dashboard/dashboard-routing.module.ts": "src/app/features/dashboard/dashboard-routing.module.ts", "src/app/features/dashboard/dashboard.module.ts": "src/app/features/dashboard/dashboard.module.ts", "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.html": "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.html", "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.scss": "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.scss", "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.spec.ts": "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.spec.ts", "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.ts": "src/app/features/dashboard/pages/dashboard-page/dashboard-page.component.ts", "src/app/shared/components/ai-highlights/ai-highlights.component.html": "src/app/shared/components/ai-highlights/ai-highlights.component.html", "src/app/shared/components/ai-highlights/ai-highlights.component.scss": "src/app/shared/components/ai-highlights/ai-highlights.component.scss", "src/app/shared/components/ai-highlights/ai-highlights.component.ts": "src/app/shared/components/ai-highlights/ai-highlights.component.ts", "src/app/shared/components/buttons/button.component.html": "src/app/shared/components/buttons/button.component.html", "src/app/shared/components/buttons/button.component.scss": "src/app/shared/components/buttons/button.component.scss", "src/app/shared/components/buttons/button.component.ts": "src/app/shared/components/buttons/button.component.ts", "src/app/shared/components/component-test/component-test.component.html": "src/app/shared/components/component-test/component-test.component.html", "src/app/shared/components/component-test/component-test.component.scss": "src/app/shared/components/component-test/component-test.component.scss", "src/app/shared/components/component-test/component-test.component.ts": "src/app/shared/components/component-test/component-test.component.ts", "src/app/shared/components/demographics/demographics.component.html": "src/app/shared/components/demographics/demographics.component.html", "src/app/shared/components/demographics/demographics.component.scss": "src/app/shared/components/demographics/demographics.component.scss", "src/app/shared/components/demographics/demographics.component.ts": "src/app/shared/components/demographics/demographics.component.ts", "src/app/shared/components/form-controls/calendar/calendar.component.html": "src/app/shared/components/form-controls/calendar/calendar.component.html", "src/app/shared/components/form-controls/calendar/calendar.component.scss": "src/app/shared/components/form-controls/calendar/calendar.component.scss", "src/app/shared/components/form-controls/calendar/calendar.component.ts": "src/app/shared/components/form-controls/calendar/calendar.component.ts", "src/app/shared/components/form-controls/checkbox/checkbox.component.html": "src/app/shared/components/form-controls/checkbox/checkbox.component.html", "src/app/shared/components/form-controls/checkbox/checkbox.component.scss": "src/app/shared/components/form-controls/checkbox/checkbox.component.scss", "src/app/shared/components/form-controls/checkbox/checkbox.component.ts": "src/app/shared/components/form-controls/checkbox/checkbox.component.ts", "src/app/shared/components/form-controls/comment-box/comment-box.component.html": "src/app/shared/components/form-controls/comment-box/comment-box.component.html", "src/app/shared/components/form-controls/comment-box/comment-box.component.scss": "src/app/shared/components/form-controls/comment-box/comment-box.component.scss", "src/app/shared/components/form-controls/comment-box/comment-box.component.ts": "src/app/shared/components/form-controls/comment-box/comment-box.component.ts", "src/app/shared/components/form-controls/dropdown/dropdown.component.html": "src/app/shared/components/form-controls/dropdown/dropdown.component.html", "src/app/shared/components/form-controls/dropdown/dropdown.component.scss": "src/app/shared/components/form-controls/dropdown/dropdown.component.scss", "src/app/shared/components/form-controls/dropdown/dropdown.component.ts": "src/app/shared/components/form-controls/dropdown/dropdown.component.ts", "src/app/shared/components/form-controls/reasoning-dropdown/reasoning-dropdown.component.ts": "src/app/shared/components/form-controls/reasoning-dropdown/reasoning-dropdown.component.ts", "src/app/shared/components/form-controls/results/results.component.html": "src/app/shared/components/form-controls/results/results.component.html", "src/app/shared/components/form-controls/results/results.component.scss": "src/app/shared/components/form-controls/results/results.component.scss", "src/app/shared/components/form-controls/results/results.component.ts": "src/app/shared/components/form-controls/results/results.component.ts", "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.html": "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.html", "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.scss": "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.scss", "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.ts": "src/app/shared/components/form-controls/results/tabs/exclusions-tab.component.ts", "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.html": "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.html", "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.scss": "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.scss", "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.ts": "src/app/shared/components/form-controls/results/tabs/inclusions-tab.component.ts", "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.html": "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.html", "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.scss": "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.scss", "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.ts": "src/app/shared/components/form-controls/results/tabs/none-found-tab.component.ts", "src/app/shared/components/form-controls/value-input/value-input.component.html": "src/app/shared/components/form-controls/value-input/value-input.component.html", "src/app/shared/components/form-controls/value-input/value-input.component.scss": "src/app/shared/components/form-controls/value-input/value-input.component.scss", "src/app/shared/components/form-controls/value-input/value-input.component.ts": "src/app/shared/components/form-controls/value-input/value-input.component.ts", "src/app/shared/components/header/header.component.html": "src/app/shared/components/header/header.component.html", "src/app/shared/components/header/header.component.scss": "src/app/shared/components/header/header.component.scss", "src/app/shared/components/header/header.component.ts": "src/app/shared/components/header/header.component.ts", "src/app/shared/components/hits/hits.component.html": "src/app/shared/components/hits/hits.component.html", "src/app/shared/components/hits/hits.component.scss": "src/app/shared/components/hits/hits.component.scss", "src/app/shared/components/hits/hits.component.ts": "src/app/shared/components/hits/hits.component.ts", "src/app/shared/components/icons/icon.component.html": "src/app/shared/components/icons/icon.component.html", "src/app/shared/components/icons/icon.component.scss": "src/app/shared/components/icons/icon.component.scss", "src/app/shared/components/icons/icon.component.ts": "src/app/shared/components/icons/icon.component.ts", "src/app/shared/components/icons/refresh-icon.component.scss": "src/app/shared/components/icons/refresh-icon.component.scss", "src/app/shared/components/icons/refresh-icon.component.ts": "src/app/shared/components/icons/refresh-icon.component.ts", "src/app/shared/components/menu/menu.component.html": "src/app/shared/components/menu/menu.component.html", "src/app/shared/components/menu/menu.component.scss": "src/app/shared/components/menu/menu.component.scss", "src/app/shared/components/menu/menu.component.ts": "src/app/shared/components/menu/menu.component.ts", "src/app/shared/components/notes/notes.component.html": "src/app/shared/components/notes/notes.component.html", "src/app/shared/components/notes/notes.component.scss": "src/app/shared/components/notes/notes.component.scss", "src/app/shared/components/notes/notes.component.ts": "src/app/shared/components/notes/notes.component.ts", "src/app/shared/components/page-title/page-title.component.scss": "src/app/shared/components/page-title/page-title.component.scss", "src/app/shared/components/page-title/page-title.component.ts": "src/app/shared/components/page-title/page-title.component.ts", "src/app/shared/shared.module.ts": "src/app/shared/shared.module.ts", "src/assets/CSVs/MRSDS_Template_CQ(Template).csv": "src/assets/CSVs/MRSDS_Template_CQ(Template).csv", "src/assets/fonts/urbane/Urbane-Bold.eot": "src/assets/fonts/urbane/Urbane-Bold.eot", "src/assets/fonts/urbane/Urbane-Bold.ttf": "src/assets/fonts/urbane/Urbane-Bold.ttf", "src/assets/fonts/urbane/Urbane-Bold.woff": "src/assets/fonts/urbane/Urbane-Bold.woff", "src/assets/fonts/urbane/Urbane-Bold.woff2": "src/assets/fonts/urbane/Urbane-Bold.woff2", "src/assets/fonts/urbane/Urbane-BoldItalic.eot": "src/assets/fonts/urbane/Urbane-BoldItalic.eot", "src/assets/fonts/urbane/Urbane-BoldItalic.ttf": "src/assets/fonts/urbane/Urbane-BoldItalic.ttf", "src/assets/fonts/urbane/Urbane-BoldItalic.woff": "src/assets/fonts/urbane/Urbane-BoldItalic.woff", "src/assets/fonts/urbane/Urbane-BoldItalic.woff2": "src/assets/fonts/urbane/Urbane-BoldItalic.woff2", "src/assets/fonts/urbane/Urbane-DemiBold.eot": "src/assets/fonts/urbane/Urbane-DemiBold.eot", "src/assets/fonts/urbane/Urbane-DemiBold.ttf": "src/assets/fonts/urbane/Urbane-DemiBold.ttf", "src/assets/fonts/urbane/Urbane-DemiBold.woff": "src/assets/fonts/urbane/Urbane-DemiBold.woff", "src/assets/fonts/urbane/Urbane-DemiBold.woff2": "src/assets/fonts/urbane/Urbane-DemiBold.woff2", "src/assets/fonts/urbane/Urbane-DemiBoldItalic.eot": "src/assets/fonts/urbane/Urbane-DemiBoldItalic.eot", "src/assets/fonts/urbane/Urbane-DemiBoldItalic.ttf": "src/assets/fonts/urbane/Urbane-DemiBoldItalic.ttf", "src/assets/fonts/urbane/Urbane-DemiBoldItalic.woff": "src/assets/fonts/urbane/Urbane-DemiBoldItalic.woff", "src/assets/fonts/urbane/Urbane-DemiBoldItalic.woff2": "src/assets/fonts/urbane/Urbane-DemiBoldItalic.woff2", "src/assets/fonts/urbane/Urbane-ExtraLight.eot": "src/assets/fonts/urbane/Urbane-ExtraLight.eot", "src/assets/fonts/urbane/Urbane-ExtraLight.ttf": "src/assets/fonts/urbane/Urbane-ExtraLight.ttf", "src/assets/fonts/urbane/Urbane-ExtraLight.woff": "src/assets/fonts/urbane/Urbane-ExtraLight.woff", "src/assets/fonts/urbane/Urbane-ExtraLight.woff2": "src/assets/fonts/urbane/Urbane-ExtraLight.woff2", "src/assets/fonts/urbane/Urbane-ExtraLightItalic.eot": "src/assets/fonts/urbane/Urbane-ExtraLightItalic.eot", "src/assets/fonts/urbane/Urbane-ExtraLightItalic.ttf": "src/assets/fonts/urbane/Urbane-ExtraLightItalic.ttf", "src/assets/fonts/urbane/Urbane-ExtraLightItalic.woff": "src/assets/fonts/urbane/Urbane-ExtraLightItalic.woff", "src/assets/fonts/urbane/Urbane-ExtraLightItalic.woff2": "src/assets/fonts/urbane/Urbane-ExtraLightItalic.woff2", "src/assets/fonts/urbane/Urbane-Heavy.eot": "src/assets/fonts/urbane/Urbane-Heavy.eot", "src/assets/fonts/urbane/Urbane-Heavy.ttf": "src/assets/fonts/urbane/Urbane-Heavy.ttf", "src/assets/fonts/urbane/Urbane-Heavy.woff": "src/assets/fonts/urbane/Urbane-Heavy.woff", "src/assets/fonts/urbane/Urbane-Heavy.woff2": "src/assets/fonts/urbane/Urbane-Heavy.woff2", "src/assets/fonts/urbane/Urbane-HeavyItalic.eot": "src/assets/fonts/urbane/Urbane-HeavyItalic.eot", "src/assets/fonts/urbane/Urbane-HeavyItalic.ttf": "src/assets/fonts/urbane/Urbane-HeavyItalic.ttf", "src/assets/fonts/urbane/Urbane-HeavyItalic.woff": "src/assets/fonts/urbane/Urbane-HeavyItalic.woff", "src/assets/fonts/urbane/Urbane-HeavyItalic.woff2": "src/assets/fonts/urbane/Urbane-HeavyItalic.woff2", "src/assets/fonts/urbane/Urbane-Light.eot": "src/assets/fonts/urbane/Urbane-Light.eot", "src/assets/fonts/urbane/Urbane-Light.ttf": "src/assets/fonts/urbane/Urbane-Light.ttf", "src/assets/fonts/urbane/Urbane-Light.woff": "src/assets/fonts/urbane/Urbane-Light.woff", "src/assets/fonts/urbane/Urbane-Light.woff2": "src/assets/fonts/urbane/Urbane-Light.woff2", "src/assets/fonts/urbane/Urbane-LightItalic.eot": "src/assets/fonts/urbane/Urbane-LightItalic.eot", "src/assets/fonts/urbane/Urbane-LightItalic.ttf": "src/assets/fonts/urbane/Urbane-LightItalic.ttf", "src/assets/fonts/urbane/Urbane-LightItalic.woff": "src/assets/fonts/urbane/Urbane-LightItalic.woff", "src/assets/fonts/urbane/Urbane-LightItalic.woff2": "src/assets/fonts/urbane/Urbane-LightItalic.woff2", "src/assets/fonts/urbane/Urbane-Medium.eot": "src/assets/fonts/urbane/Urbane-Medium.eot", "src/assets/fonts/urbane/Urbane-Medium.ttf": "src/assets/fonts/urbane/Urbane-Medium.ttf", "src/assets/fonts/urbane/Urbane-Medium.woff": "src/assets/fonts/urbane/Urbane-Medium.woff", "src/assets/fonts/urbane/Urbane-Medium.woff2": "src/assets/fonts/urbane/Urbane-Medium.woff2", "src/assets/fonts/urbane/Urbane-MediumItalic.eot": "src/assets/fonts/urbane/Urbane-MediumItalic.eot", "src/assets/fonts/urbane/Urbane-MediumItalic.ttf": "src/assets/fonts/urbane/Urbane-MediumItalic.ttf", "src/assets/fonts/urbane/Urbane-MediumItalic.woff": "src/assets/fonts/urbane/Urbane-MediumItalic.woff", "src/assets/fonts/urbane/Urbane-MediumItalic.woff2": "src/assets/fonts/urbane/Urbane-MediumItalic.woff2", "src/assets/fonts/urbane/Urbane-Thin.eot": "src/assets/fonts/urbane/Urbane-Thin.eot", "src/assets/fonts/urbane/Urbane-Thin.ttf": "src/assets/fonts/urbane/Urbane-Thin.ttf", "src/assets/fonts/urbane/Urbane-Thin.woff": "src/assets/fonts/urbane/Urbane-Thin.woff", "src/assets/fonts/urbane/Urbane-Thin.woff2": "src/assets/fonts/urbane/Urbane-Thin.woff2", "src/assets/fonts/urbane/Urbane-ThinItalic.eot": "src/assets/fonts/urbane/Urbane-ThinItalic.eot", "src/assets/fonts/urbane/Urbane-ThinItalic.ttf": "src/assets/fonts/urbane/Urbane-ThinItalic.ttf", "src/assets/fonts/urbane/Urbane-ThinItalic.woff": "src/assets/fonts/urbane/Urbane-ThinItalic.woff", "src/assets/fonts/urbane/Urbane-ThinItalic.woff2": "src/assets/fonts/urbane/Urbane-ThinItalic.woff2", "src/assets/fonts/urbane/demo.html": "src/assets/fonts/urbane/demo.html", "src/assets/fonts/urbane/stylesheet.css": "src/assets/fonts/urbane/stylesheet.css", "src/assets/logos/Stellarus-Favicon-red.png": "src/assets/logos/Stellarus-Favicon-red.png", "src/assets/logos/Stellarus_logo_2C_blacktype.png": "src/assets/logos/Stellarus_logo_2C_blacktype.png", "src/assets/pdf.worker.mjs": "src/assets/pdf.worker.mjs", "src/environments/environment.prod.ts": "src/environments/environment.prod.ts", "src/environments/environment.ts": "src/environments/environment.ts", "src/index.html": "src/index.html", "src/main.server.ts": "src/main.server.ts", "src/main.ts": "src/main.ts", "src/server.ts": "src/server.ts", "src/styles.scss": "src/styles.scss", "src/styles/_mixins.scss": "src/styles/_mixins.scss", "src/styles/_theme.scss": "src/styles/_theme.scss", "src/styles/_typography.scss": "src/styles/_typography.scss", "src/styles/_variables.scss": "src/styles/_variables.scss", "structure.txt": "structure.txt", "tsconfig.app.json": "tsconfig.app.json", "tsconfig.json": "tsconfig.json", "tsconfig.spec.json": "tsconfig.spec.json"}}