import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent {
  @Input() userName: string = '<PERSON>'; // Updated default username
  @Input() logoUrl: string = 'assets/logos/Stellarus_logo_2C_blacktype.png';

  getAvatarInitials(): string {
    if (!this.userName) {
      return '';
    }
    const names = this.userName.split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    }
    return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
  }
}