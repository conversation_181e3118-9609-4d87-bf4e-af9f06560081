import { Injectable, Inject } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { catchError, map, timeout } from 'rxjs/operators';
import { DatabricksConfig, DATABRICKS_CONFIG_TOKEN } from '../config/databricks.config';

export interface DatabricksFileInfo {
  path: string;
  name: string;
  size: number;
  modified_at: number;
  is_directory: boolean;
}

export interface DatabricksListResponse {
  files: DatabricksFileInfo[];
}

@Injectable({ providedIn: 'root' })
export class DatabricksService {
  private readonly apiVersion = '2.0';

  constructor(
    private http: HttpClient,
    @Inject(DATABRICKS_CONFIG_TOKEN) private config: DatabricksConfig
  ) {}

  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Authorization': `Bearer ${this.config.token}`,
      'Content-Type': 'application/json'
    });
  }

  private getBaseUrl(): string {
    return this.config.baseUrl?.replace(/\/$/, '') || '';
  }

  private useProxy(): boolean {
    return !!this.config.useProxy;
  }

  private proxyPath(endpoint: string): string {
    const base = this.config.proxyBasePath || '/api/databricks';
    return `${base}${endpoint}`;
  }

  listFiles(path: string = ''): Observable<DatabricksFileInfo[]> {
    const fullPath = `${this.config.volumePath}/${path}`.replace(/\/+/g, '/');

    const url = this.useProxy()
      ? this.proxyPath('/fs/list')
      : `${this.getBaseUrl()}/api/${this.apiVersion}/fs/list`;

    const body = { path: fullPath };

    const options = this.useProxy()
      ? {}
      : { headers: this.getHeaders() };

    return this.http.post<DatabricksListResponse>(url, body, options as any).pipe(
      timeout(this.config.timeout || 30000),
      map(response => (response as any).files || []),
      catchError(this.handleError)
    );
  }

  downloadPdf(fileName: string, subPath: string = ''): Observable<ArrayBuffer> {
    const filePath = subPath
      ? `${this.config.volumePath}/${subPath}/${fileName}`.replace(/\/+/g, '/')
      : `${this.config.volumePath}/${fileName}`.replace(/\/+/g, '/');

    const url = this.useProxy()
      ? this.proxyPath('/fs/download')
      : `${this.getBaseUrl()}/api/${this.apiVersion}/fs/download`;

    const body = { path: filePath };

    const options: any = this.useProxy()
      ? { responseType: 'arraybuffer' as const }
      : { headers: this.getHeaders(), responseType: 'arraybuffer' as const };

    return this.http.post(url, body, options).pipe(
      timeout(this.config.timeout || 30000),
      catchError(this.handleError)
    );
  }

  fileExists(fileName: string, subPath: string = ''): Observable<boolean> {
    const filePath = subPath
      ? `${this.config.volumePath}/${subPath}/${fileName}`.replace(/\/+/g, '/')
      : `${this.config.volumePath}/${fileName}`.replace(/\/+/g, '/');

    const url = this.useProxy()
      ? this.proxyPath('/fs/get-status')
      : `${this.getBaseUrl()}/api/${this.apiVersion}/fs/get-status`;

    const body = { path: filePath };

    const options = this.useProxy()
      ? {}
      : { headers: this.getHeaders() };

    return this.http.post(url, body, options as any).pipe(
      timeout(this.config.timeout || 30000),
      map(() => true),
      catchError((error: HttpErrorResponse) => {
        if (error.status === 404) {
          return of(false);
        }
        return this.handleError(error);
      })
    );
  }

  getFileInfo(fileName: string, subPath: string = ''): Observable<DatabricksFileInfo> {
    const filePath = subPath
      ? `${this.config.volumePath}/${subPath}/${fileName}`.replace(/\/+/g, '/')
      : `${this.config.volumePath}/${fileName}`.replace(/\/+/g, '/');

    const url = this.useProxy()
      ? this.proxyPath('/fs/get-status')
      : `${this.getBaseUrl()}/api/${this.apiVersion}/fs/get-status`;

    const body = { path: filePath };

    const options = this.useProxy()
      ? { observe: 'body' as const, responseType: 'json' as const }
      : { headers: this.getHeaders(), observe: 'body' as const, responseType: 'json' as const };

    return this.http.post<DatabricksFileInfo>(url, body, options).pipe(
      timeout(this.config.timeout || 30000),
      catchError(this.handleError)
    );
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An error occurred while accessing Databricks';

    if (error.error instanceof ErrorEvent) {
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      switch (error.status) {
        case 0:
          errorMessage = 'Network error or CORS: Unable to reach Databricks';
          break;
        case 401:
          errorMessage = 'Unauthorized: Invalid Databricks token';
          break;
        case 403:
          errorMessage = 'Forbidden: Insufficient permissions to access Databricks volume';
          break;
        case 404:
          errorMessage = 'Not Found: File or path does not exist in Databricks volume';
          break;
        case 429:
          errorMessage = 'Rate Limited: Too many requests to Databricks API';
          break;
        case 500:
          errorMessage = 'Internal Server Error: Databricks API error';
          break;
        default:
          errorMessage = `Databricks API Error: ${error.status} - ${error.message}`;
      }
    }

    console.error('[DatabricksService]', errorMessage, error);
    return throwError(() => new Error(errorMessage));
  }

  testConnection(): Observable<boolean> {
    return this.listFiles().pipe(
      map(() => true),
      catchError(() => of(false))
    );
  }
}
