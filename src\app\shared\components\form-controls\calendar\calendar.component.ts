import { Component, Input, Output, EventEmitter, forwardRef, OnInit, HostListener, ElementRef, ViewChild, ChangeDetectorRef } from '@angular/core';

import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-calendar',
  standalone: true,
  imports: [],
  templateUrl: './calendar.component.html',
  styleUrls: ['./calendar.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CalendarComponent),
      multi: true
    }
  ]
})
export class CalendarComponent implements ControlValueAccessor, OnInit {
  @Input() label: string = '';
  @Input() placeholder: string = 'Date of Service';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() errorMessage: string = '';
  @Input() id: string = '';
  @Input() name: string = '';
  @Input() minDate: string = '';
  @Input() maxDate: string = '';

  @Output() dateChange = new EventEmitter<string>();

  @ViewChild('dateInput', { static: false }) dateInput!: ElementRef<HTMLInputElement>;
  @ViewChild('calendarContainer', { static: false }) calendarContainer!: ElementRef;

  isCalendarOpen: boolean = false;
  selectedDate: string | Date | null = null;
  displayValue: string = '';
  currentMonth: number = new Date().getMonth();
  currentYear: number = new Date().getFullYear();
  calendarDays: (number | null)[] = [];
  monthNames: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // ControlValueAccessor implementation
  onChange: any = () => {};
  onTouched: any = () => {};

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    if (!this.id) {
      this.id = 'calendar-' + Math.random().toString(36).substring(2, 9);
    }
    this.generateCalendar();
  }

 writeValue(value: Date | string | null | undefined): void {
    if (value) {
      if (typeof value === 'string') {
        const parsedDate = new Date(value);
        if (!isNaN(parsedDate.getTime())) {
          this.selectedDate = parsedDate;
        } else {
          this.selectedDate = null;
        }
      } else {
        this.selectedDate = value;
      }
    } else {
      this.selectedDate = null;
    }

    this.displayValue = this.formatDisplayValue(this.selectedDate);
    this.cdr.markForCheck();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    this.cdr.markForCheck();
  }

  onInputChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = target.value;

    this.selectedDate = value;
    this.displayValue = this.formatDisplayValue(value);
    this.onChange(value);
    this.dateChange.emit(value);
  }

  onInputFocus(): void {
    this.onTouched();
  }

  onInputBlur(): void {
    if (this.selectedDate) {
      const dateString = typeof this.selectedDate === 'string' ? this.selectedDate : this.formatDate(this.selectedDate);
      
      // Only validate and reformat if it looks like a complete date
      if (this.isCompleteDate(dateString)) {
        const formattedDate = this.validateAndFormatDate(dateString);
        const finalDate = this.formatDisplayValue(formattedDate);

        this.selectedDate = finalDate;
        this.displayValue = finalDate;
        this.onChange(finalDate);
        this.dateChange.emit(finalDate);
      } else {
        // For incomplete dates, just use the current value without reformatting
        this.displayValue = dateString;
        this.onChange(dateString);
        this.dateChange.emit(dateString);
      }
    }
  }

  toggleCalendar(): void {
    if (!this.disabled) {
      this.isCalendarOpen = !this.isCalendarOpen;
      if (this.isCalendarOpen) {
        this.onTouched();
        this.generateCalendar();
      }
    }
  }

  selectDate(day: number): void {
    if (day) {
      const selectedDate = new Date(this.currentYear, this.currentMonth, day);
      const formattedDate = this.formatDate(selectedDate);

      this.selectedDate = formattedDate;
      this.displayValue = this.formatDisplayValue(formattedDate);
      this.isCalendarOpen = false;

      this.onChange(formattedDate);
      this.dateChange.emit(formattedDate);
    }
  }

  previousMonth(): void {
    if (this.currentMonth === 0) {
      this.currentMonth = 11;
      this.currentYear--;
    } else {
      this.currentMonth--;
    }
    this.generateCalendar();
  }

  nextMonth(): void {
    if (this.currentMonth === 11) {
      this.currentMonth = 0;
      this.currentYear++;
    } else {
      this.currentMonth++;
    }
    this.generateCalendar();
  }

  generateCalendar(): void {
    const firstDay = new Date(this.currentYear, this.currentMonth, 1).getDay();
    const daysInMonth = new Date(this.currentYear, this.currentMonth + 1, 0).getDate();

    this.calendarDays = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      this.calendarDays.push(null);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      this.calendarDays.push(day);
    }
  }

  isSelectedDay(day: number | null): boolean {
    if (!day || !this.selectedDate) return false;

    const selectedDate = new Date(this.selectedDate);
    return selectedDate.getDate() === day &&
           selectedDate.getMonth() === this.currentMonth &&
           selectedDate.getFullYear() === this.currentYear;
  }

  isToday(day: number | null): boolean {
    if (!day) return false;

    const today = new Date();
    return today.getDate() === day &&
           today.getMonth() === this.currentMonth &&
           today.getFullYear() === this.currentYear;
  }

  private formatDate(date: Date): string {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear().toString().substring(2);
    return `${month}/${day}/${year}`;
  }

  private formatDisplayValue(value: Date | string | null): string {
  if (!value) return '';

  if (typeof value === 'string') {
    // If already in MM/DD/YY format, return as is
    if (/^\d{2}\/\d{2}\/\d{2}$/.test(value)) {
      return value;
    }

    const parsed = new Date(value);
    if (!isNaN(parsed.getTime())) {
      return this.formatDate(parsed);
    }

    return value; // fallback if string but unparseable
  }

  if (value instanceof Date && !isNaN(value.getTime())) {
    return this.formatDate(value);
  }

  // Explicit fallback in case something slips through
  return '';
}

  private validateAndFormatDate(value: string): string {
    if (!value) return '';

    // If already in correct MM/DD/YY format, return as is
    if (/^\d{2}\/\d{2}\/\d{2}$/.test(value)) {
      return value;
    }

    // Try to parse and format the date
    const date = new Date(value);
    if (!isNaN(date.getTime())) {
      return this.formatDate(date);
    }

    // If parsing fails, return the original value
    return value;
  }

  private isCompleteDate(value: string): boolean {
    if (!value) return false;
    
    // Check if it matches MM/DD/YY or MM/DD/YYYY pattern
    const shortPattern = /^\d{1,2}\/\d{1,2}\/\d{2}$/;
    const longPattern = /^\d{1,2}\/\d{1,2}\/\d{4}$/;
    
    return shortPattern.test(value) || longPattern.test(value);
  }
  

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (this.calendarContainer && !this.calendarContainer.nativeElement.contains(event.target as Node)) {
      this.isCalendarOpen = false;
    }
  }

  trackByDay(index: number, day: number | null): any {
    return day !== null ? day : index;
  }
  
}
