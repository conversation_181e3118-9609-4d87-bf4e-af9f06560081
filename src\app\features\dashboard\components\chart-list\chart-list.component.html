<div class="chart-list-container">
  <div class="chart-list-header">
    @for (column of columns; track column) {
      <div
        class="chart-list-header-cell"
        [ngStyle]="{'width': column.width}">
        {{ column.header }}
      </div>
    }
  </div>

  <div class="chart-list-body">
    @if (filteredCharts.length > 0) {
      @for (chart of filteredCharts; track chart) {
        <div class="chart-list-row">
          @for (column of columns; track column) {
            <div
              class="chart-list-cell"
              [ngStyle]="{'width': column.width}">
              @if (column.field !== 'status') {
                {{ chart[column.field] }}
              } @else {
                <div class="status-badge"
                  [ngClass]="getStatusClass(chart.status)"
                  (click)="navigateToChartReview(chart)"
                  [class.clickable]="chart.status && chart.status.toLowerCase() === 'review'">
                  {{ chart.status }}
                </div>
              }
            </div>
          }
        </div>
      }
    } @else {
      <div class="no-charts-message">
        No charts found matching your criteria.
      </div>
    }

  </div>
</div>