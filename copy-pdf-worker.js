const fs = require('fs');
const path = require('path');

// Get the version of pdfjs-dist from package.json
const packageJson = require('./package.json');
const pdfjsVersion = packageJson.dependencies['pdfjs-dist'].replace('^', '');

// Source path in node_modules
const sourcePath = path.join(
  __dirname,
  'node_modules',
  'pdfjs-dist',
  'build',
  'pdf.worker.mjs'
);

// Destination directory in assets
const destDir = path.join(__dirname, 'src', 'assets');
const destPath = path.join(destDir, 'pdf.worker.mjs');

// Ensure the destination directory exists
if (!fs.existsSync(destDir)) {
  fs.mkdirSync(destDir, { recursive: true });
}

// Copy the file
try {
  fs.copyFileSync(sourcePath, destPath);
  console.log(`Successfully copied pdf.worker.mjs (version ${pdfjsVersion}) to src/assets/`);
} catch (err) {
  console.error('Error copying pdf.worker.mjs:', err);
  process.exit(1);
}