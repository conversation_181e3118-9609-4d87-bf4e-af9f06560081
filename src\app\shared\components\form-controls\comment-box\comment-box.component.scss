@use 'variables' as variables;

// Comment Box Component - Based on Figma comment-box.html specifications
.comment-box {
  flex: 1 1 0; // Exact Figma flex
  height: 30px; // Exact Figma height
  padding: 4px; // Exact Figma padding
  background: white !important; // Exact Figma background - CRITICAL for visibility
  overflow: hidden; // Exact Figma overflow
  border-radius: 10px; // Exact Figma border radius
  outline-offset: -1px; // Exact Figma outline offset
  flex-direction: column; // Exact Figma flex-direction
  justify-content: center; // Exact Figma justify
  align-items: center; // Exact Figma align
  display: inline-flex; // Exact Figma display
  box-sizing: border-box;

  // Default state - gray border
  &.default {
    outline: 1px #D9E1E7 solid; // Exact Figma outline (var(--gray-2))
  }

  // Active state - darker gray border when focused
  &.active {
    outline: 1px #547996 solid; // Exact Figma outline (var(--gray-3))
  }

  // Entered state - gray border when has content
  &.entered {
    outline: 1px #D9E1E7 solid; // Exact Figma outline (var(--gray-2))
  }

  // Disabled state
  &.disabled {
    background-color: variables.$gray-1;
    cursor: not-allowed;
  }
}

.row {
  align-self: stretch; // Exact Figma align-self
  padding-left: 12px; // Exact Figma padding
  padding-right: 12px; // Exact Figma padding
  background: transparent; // Ensure no background override
  border-radius: 6px; // Exact Figma border radius
  justify-content: flex-start; // Exact Figma justify
  align-items: center; // Exact Figma align
  gap: 12px; // Exact Figma gap
  display: inline-flex; // Exact Figma display
}

.comment-input {
  flex: 1 1 0; // Exact Figma flex
  border: none !important;
  outline: none !important;
  background: transparent !important;
  color: #17181A !important; // Ensure text is visible
  font-size: 10px; // Exact Figma font size
  font-family: 'Urbane', sans-serif; // Exact Figma font family
  font-weight: 300; // Exact Figma font weight
  line-height: 12px; // Exact Figma line height
  word-wrap: break-word; // Exact Figma word wrap
  padding: 0;
  width: 100%;
  height: 100%;

  // Default state - gray placeholder
  &::placeholder {
    color: #547996; // Exact Figma color (var(--gray-3))
  }

  // Active and Entered states - black text
  .active &,
  .entered & {
    color: #17181A; // Exact Figma color (var(--text-black))
  }

  // Default state - gray text
  .default & {
    color: #547996; // Exact Figma color (var(--gray-3))
  }

  &:disabled {
    cursor: not-allowed;
    color: variables.$gray-3;
  }

  &:focus {
    outline: none;
  }
}
