/**
 * Data models for chart data and CSV integration
 */

// Raw CSV data structure matching the CSV file format
export interface RawCsvChartData {
  FILENAME: string;
  MBR_LNAME: string;
  MBR_MNAME: string;
  MBR_FNAME: string;
  MBR_DOB: string;
  LOB: string;
  MeasureKey: string;
  BSC_MBR_ID: string;
  MBR_GENDER: string;
  PRVR_NPI: string;
  PRVR_LNAME: string;
  PRVR_FNAME: string;
  PDF_STATUS: string;
}

// Provider information model
export interface ProviderInfo {
  npi: string;
  firstName: string;
  lastName: string;
  fullName: string;
}

// Enhanced chart data model for UI consumption
export interface AssignedChart {
  memberId: string;
  firstName: string;
  middleName: string;
  lastName: string;
  fullName: string;
  dob: string;
  lob: string;
  measure: string;
  measureKey: string;
  gender: string;
  filename: string;
  provider: ProviderInfo;
  review1: string;
  review2: string;
  assigned: string;
  status: ChartStatus;
}

// Chart status enumeration
export type ChartStatus = 'Review' | 'Complete' | 'Inactive';

// Data loading states
export interface DataLoadingState {
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

// CSV parsing result
export interface CsvParseResult {
  data: AssignedChart[];
  errors: string[];
  meta: {
    delimiter: string;
    linebreak: string;
    aborted: boolean;
    truncated: boolean;
    cursor: number;
  };
}

// Service response wrapper
export interface ChartDataResponse {
  charts: AssignedChart[];
  loadingState: DataLoadingState;
  totalCount: number;
}

// Filter and search options
export interface ChartFilterOptions {
  searchText?: string;
  status?: ChartStatus[];
  measure?: string[];
  lob?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
}

// Column definition for table display
export interface TableColumn {
  field: keyof AssignedChart | string;
  header: string;
  width: string;
  sortable?: boolean;
  filterable?: boolean;
}

// Default column definitions matching Figma specifications
export const DEFAULT_TABLE_COLUMNS: TableColumn[] = [
  { field: 'memberId', header: 'Member ID', width: '140px', sortable: true },
  { field: 'firstName', header: 'First name', width: '107px', sortable: true },
  { field: 'lastName', header: 'Last name', width: '106px', sortable: true },
  { field: 'middleName', header: 'Middle name', width: '102px', sortable: true },
  { field: 'dob', header: 'DOB', width: '99px', sortable: true },
  { field: 'lob', header: 'LOB', width: '100px', sortable: true, filterable: true },
  { field: 'measure', header: 'Measure', width: '93px', sortable: true, filterable: true },
  { field: 'review1', header: 'Review 1', width: '140px', sortable: true },
  { field: 'review2', header: 'Review 2', width: '140px', sortable: true },
  { field: 'assigned', header: 'Assigned', width: '170px', sortable: true },
  { field: 'status', header: 'Status', width: '118px', sortable: true, filterable: true }
];

// Utility type for CSV field mapping
export type CsvFieldMapping = {
  [K in keyof RawCsvChartData]: keyof AssignedChart | ((value: string) => any);
};

// Default field mapping configuration
export const CSV_FIELD_MAPPING: Partial<CsvFieldMapping> = {
  BSC_MBR_ID: 'memberId',
  MBR_FNAME: 'firstName',
  MBR_MNAME: 'middleName',
  MBR_LNAME: 'lastName',
  MBR_DOB: 'dob',
  LOB: 'lob',
  MeasureKey: 'measureKey',
  MBR_GENDER: 'gender',
  FILENAME: 'filename'
};
