# Databricks PDF Integration

This document describes how to configure and use the Databricks integration for loading PDF files from a Databricks volume instead of local assets.

## Overview

The application has been updated to support loading PDF files directly from a Databricks volume with automatic fallback to local assets if the Databricks source is unavailable.

## Configuration

### Environment Variables

Set the following environment variables for production deployment:

```bash
DATABRICKS_BASE_URL=https://your-databricks-workspace.cloud.databricks.com
DATABRICKS_TOKEN=your-databricks-personal-access-token
DATABRICKS_VOLUME_PATH=/Volumes/catalog/schema/volume/charts
```

### Development Configuration

For development, update the values in `src/environments/environment.ts`:

```typescript
export const environment = {
  production: false,
  databricks: {
    baseUrl: 'https://your-databricks-workspace.cloud.databricks.com',
    token: 'your-databricks-personal-access-token',
    volumePath: '/Volumes/catalog/schema/volume/charts',
    timeout: 30000
  }
};
```

## Databricks Setup

### 1. Create a Volume

Create a Unity Catalog volume to store your PDF files:

```sql
CREATE VOLUME IF NOT EXISTS catalog.schema.charts_volume;
```

### 2. Upload PDF Files

Upload your PDF files to the volume using Databricks CLI or the web interface:

```bash
databricks fs cp local-file.pdf dbfs:/Volumes/catalog/schema/charts_volume/
```

### 3. Generate Personal Access Token

1. Go to your Databricks workspace
2. Click on your user profile → Settings
3. Go to Developer → Access tokens
4. Generate a new token with appropriate permissions

### 4. Set Permissions

Ensure your token has read access to the volume:

```sql
GRANT READ FILES ON VOLUME catalog.schema.charts_volume TO `your-service-principal`;
```

## API Endpoints Used

The integration uses the following Databricks REST API endpoints:

- **List Files**: `POST /api/2.0/fs/list`
- **Download File**: `POST /api/2.0/fs/download`
- **Get File Status**: `POST /api/2.0/fs/get-status`

## How It Works

### Loading Process

1. **Primary**: Try to load PDF from Databricks volume
2. **Fallback 1**: If Databricks fails, try local assets folder
3. **Fallback 2**: If specific PDF not found, try fallback PDFs in order:
   - `CBP_Redacted_Usability.pdf`
   - `55820474.pdf`

### New Service Methods

The `PdfService` now includes these new methods:

- `loadPdfFromDatabricks(fileName, subPath)` - Load PDF from Databricks volume
- `loadPdfWithFallback(chartId)` - Load with automatic fallback logic
- `checkPdfExistsInDatabricks(fileName, subPath)` - Check if PDF exists
- `listPdfsInDatabricks(subPath)` - List available PDFs

### Error Handling

The integration includes comprehensive error handling for:

- Network connectivity issues
- Authentication failures
- File not found errors
- Rate limiting
- Timeout scenarios

## File Structure

```
src/
├── app/
│   ├── core/
│   │   ├── config/
│   │   │   └── databricks.config.ts          # Databricks configuration
│   │   └── services/
│   │       └── databricks.service.ts         # Databricks API service
│   └── features/
│       └── chart-review/
│           └── services/
│               └── pdf.service.ts            # Updated PDF service
├── environments/
│   ├── environment.ts                        # Development config
│   └── environment.prod.ts                   # Production config
└── assets/
    └── charts/                               # Fallback PDF files
```

## Testing

### Test Databricks Connection

You can test the Databricks connection using the service:

```typescript
constructor(private databricksService: DatabricksService) {}

testConnection() {
  this.databricksService.testConnection().subscribe({
    next: (success) => console.log('Connection successful:', success),
    error: (error) => console.error('Connection failed:', error)
  });
}
```

### List Available PDFs

```typescript
this.pdfService.listPdfsInDatabricks().subscribe({
  next: (files) => console.log('Available PDFs:', files),
  error: (error) => console.error('Error listing files:', error)
});
```

## Security Considerations

1. **Token Security**: Never commit tokens to version control
2. **Environment Variables**: Use secure environment variable management
3. **Permissions**: Follow principle of least privilege for volume access
4. **Network Security**: Ensure secure network connectivity to Databricks

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check your Databricks token
2. **403 Forbidden**: Verify volume permissions
3. **404 Not Found**: Check volume path and file names
4. **Timeout**: Increase timeout value in configuration
5. **CORS Issues**: Ensure proper CORS configuration in Databricks

### Debug Logging

The services include comprehensive logging. Check browser console for:

- `[DatabricksService]` - Databricks API interactions
- `[PdfService]` - PDF loading operations
- `[ChartReviewPage]` - Component-level operations

## Migration from Local Assets

The integration maintains backward compatibility. Existing local PDF files in `src/assets/charts/` will continue to work as fallbacks.

To migrate:

1. Upload existing PDFs to Databricks volume
2. Configure Databricks settings
3. Test the integration
4. Optionally remove local assets once confirmed working

## Performance Considerations

- PDFs are cached after first load
- Fallback logic minimizes user-facing errors
- Timeout configuration prevents hanging requests
- Base64 conversion happens in browser for security