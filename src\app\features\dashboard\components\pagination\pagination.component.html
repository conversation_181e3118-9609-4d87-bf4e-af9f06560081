<div class="pagination-container">
  <div class="pagination-controls">
    <button 
      class="pagination-button prev-button" 
      [class.disabled]="!hasPrevious">
      <span class="pagination-icon prev-icon"></span>
      <span>Prev</span>
    </button>
    
    <div class="pagination-pages">
      <button 
        *ngFor="let page of pages" 
        class="pagination-page-button"
        [class.active]="page === currentPage">
        {{ page < 10 ? '0' + page : page }}
      </button>
    </div>
    
    <button 
      class="pagination-button next-button" 
      [class.disabled]="!hasNext">
      <span>Next</span>
      <span class="pagination-icon next-icon"></span>
    </button>
  </div>
</div>