import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { FormGroup, ReactiveFormsModule, FormControl } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NotesComponent } from '../../../notes/notes.component';
import { ValueInputComponent } from '../../../form-controls/value-input/value-input.component';
import { CalendarComponent } from '../../../form-controls/calendar/calendar.component';
import { CheckboxComponent } from '../../../form-controls/checkbox/checkbox.component';

@Component({
  selector: 'app-inclusions-tab',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NotesComponent,
    ValueInputComponent,
    CalendarComponent,
    CheckboxComponent
  ],
  templateUrl: './inclusions-tab.component.html',
  styleUrls: ['./inclusions-tab.component.scss']
})
export class InclusionsTabComponent implements OnInit, OnChanges {
  @Input() formGroup!: FormGroup;
  @Input() id: string = '';
  

  constructor() {console.log('🧩 [ValueInput] component initialized');}

    ngOnInit(): void {
      console.log('🚨 INIT FormGroup structure at tab init:', this.formGroup.value);
      console.log('🧬 INIT Controls present:', Object.keys(this.formGroup.controls));
    this.initFormControls();
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log('🚨 CHANGE FormGroup structure at tab init:', this.formGroup.value);
    console.log('🧬 CHANGE Controls present:', Object.keys(this.formGroup.controls));
  }

  private initFormControls(): void {
    const controlConfig = { value: '', disabled: false };

    if (!this.formGroup.get('sys')) {
      this.formGroup.addControl('sys', new FormControl(controlConfig));
    }

    if (!this.formGroup.get('dias')) {
      this.formGroup.addControl('dias', new FormControl(controlConfig));
    }

    if (!this.formGroup.get('dateOfService')) {
      this.formGroup.addControl('dateOfService', new FormControl(controlConfig));
    }

    if (!this.formGroup.get('telehealth')) {
      this.formGroup.addControl('telehealth', new FormControl({ value: false, disabled: false }));
    }

    console.log('✅ Controls initialized:', this.formGroup.controls);
    

  }


  // Triggered on any field change (hooked up in template)
  onFormFieldChange(field: string): void {
    const control = this.formGroup.get(field);
    if (control) {
      console.log(`✏️ ${field} changed to:`, control.value);
      // You could emit changes, perform validation, or trigger calculations here
    }
  }
  handleHighlightSelection(data: { highlight: any; page: number }): void {
    const { highlight } = data;

    if (highlight) {
      // Convert numeric values to strings for form controls
      const systolicValue = highlight.systolic ? highlight.systolic.toString() : '';
      const diastolicValue = highlight.diastolic ? highlight.diastolic.toString() : '';
      
      // Update the form controls directly
      this.formGroup.get('sys')?.setValue(systolicValue);
      this.formGroup.get('dias')?.setValue(diastolicValue);
      this.formGroup.get('dateOfService')?.setValue(highlight.dateOfService);

      // Force change detection
      setTimeout(() => {
        console.log('[InclusionsTab] Form fields updated with highlight data:', highlight);
        console.log('[InclusionsTab] Current form values:', this.formGroup.value);
      }, 0);
    }
  }
}

 /*
  ngOnChanges(change: SimpleChanges): void {
    // Initialize form controls if they don't exist with proper disabled state
    const controlConfig = { value: '', disabled: this.disabled };
    const checkboxConfig = { value: false, disabled: this.disabled };

    
    if (!this.formGroup.get('sys')) {
      this.formGroup.addControl('sys', new FormControl(controlConfig));
    }

    if (!this.formGroup.get('dias')) {
      this.formGroup.addControl('dias', new FormControl(controlConfig));
    }
    
    if (!this.formGroup.get('telehealth')) {
      this.formGroup.addControl('telehealth', new FormControl(checkboxConfig));
    } 
    
    if (!this.formGroup.get('dateOfService')) {
      this.formGroup.addControl('dateOfService', new FormControl(controlConfig));
    } 
    
    if (!this.formGroup.get('notes')) {
      this.formGroup.addControl('notes', new FormControl(controlConfig));
    } 
  }

  handleHighlightSelection(data: { highlight: any; page: number }): void {
    const { highlight } = data;

    if (highlight) {
      // Convert numeric values to strings for form controls
      const systolicValue = highlight.systolic ? highlight.systolic.toString() : '';
      const diastolicValue = highlight.diastolic ? highlight.diastolic.toString() : '';
      
      // Update the form controls directly
      this.formGroup.get('sys')?.setValue(systolicValue);
      this.formGroup.get('dias')?.setValue(diastolicValue);
      this.formGroup.get('dateOfService')?.setValue(highlight.dateOfService);

      // Force change detection
      setTimeout(() => {
        console.log('[InclusionsTab] Form fields updated with highlight data:', highlight);
        console.log('[InclusionsTab] Current form values:', this.formGroup.value);
      }, 0);
    }
  }

  onFormFieldChange(): void {
    console.log('[InclusionsTab] Form field changed');
  }
}
*/