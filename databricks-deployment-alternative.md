# Option 2: Python Flask Wrapper (Complex Approach)

## This would require significant refactoring:

### 1. Create Flask Backend
```python
# app.py
from flask import Flask, render_template, send_from_directory
import os

app = Flask(__name__, static_folder='static', template_folder='templates')

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/static/<path:filename>')
def serve_static(filename):
    return send_from_directory('static', filename)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080)
```

### 2. Build Angular and Move Files
```bash
# Build Angular app
npm run build

# Copy built files to Flask structure
mkdir -p templates static
cp dist/clinical-quality-app/index.html templates/
cp -r dist/clinical-quality-app/* static/
```

### 3. Create Databricks Files
```yaml
# app.yaml
command:
  - python
  - app.py
env:
  - name: FLASK_ENV
    value: production
```

```txt
# requirements.txt
Flask==2.3.3
```

### 4. Deploy to Databricks
```bash
databricks sync --watch . /Workspace/Users/<USER>/cce-test-1
databricks apps deploy cce-test-1 --source-code-path /Workspace/Users/<USER>/cce-test-1
```

## Challenges with This Approach:
- Complex routing setup needed
- API calls may not work properly
- PDF viewer integration issues
- Loss of Angular's SPA benefits
- Maintenance overhead
