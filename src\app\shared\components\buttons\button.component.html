<button
  [type]="type"
  [disabled]="disabled || figmaState === 'inactive'"
  [ngClass]="[
    'button',
    'button-' + variant,
    disabled ? 'button-disabled' : '',
    icon ? 'button-with-icon' : '',
    icon && iconPosition === 'right' ? 'button-icon-right' : 'button-icon-left',
    figmaExact ? 'figma-exact' : '',
    figmaState ? 'figma-state-' + figmaState : ''
  ]"
  (click)="onClick($event)"
  (mousedown)="onMouseDown()"
  (mouseup)="onMouseUp()"
  (mouseleave)="onMouseLeave()">
  @if (icon && iconPosition === 'left') {
    <span class="button-icon">
      <i class="icon-{{icon}}"></i>
    </span>
  }
  <ng-content></ng-content>
  @if (icon && iconPosition === 'right') {
    <span class="button-icon">
      <i class="icon-{{icon}}"></i>
    </span>
  }
</button>