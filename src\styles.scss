@use './styles/variables' as variables;
@use './styles/typography' as type;
@use './styles/mixins' as mix;
@use './styles/theme' as theme;


/* Global Styles */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  background-color: variables.$light-background;
}

body {
  font-family: 'Urbane', sans-serif;
  color: variables.$text-black;
}

/* Utility Classes */

// Display
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }
.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }
.align-items-center { align-items: center; }
.align-items-baseline { align-items: baseline; }
.align-items-stretch { align-items: stretch; }
.align-self-start { align-self: flex-start; }
.align-self-end { align-self: flex-end; }
.align-self-center { align-self: center; }
.align-self-baseline { align-self: baseline; }
.align-self-stretch { align-self: stretch; }
.flex-grow-0 { flex-grow: 0; }
.flex-grow-1 { flex-grow: 1; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-shrink-1 { flex-shrink: 1; }

// Spacing
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }
.mx-0 { margin-left: 0; margin-right: 0; }
.my-0 { margin-top: 0; margin-bottom: 0; }
.m-1 { margin: variables.$spacing-xs; }
.mt-1 { margin-top: variables.$spacing-xs; }
.mr-1 { margin-right: variables.$spacing-xs; }
.mb-1 { margin-bottom: variables.$spacing-xs; }
.ml-1 { margin-left: variables.$spacing-xs; }
.mx-1 { margin-left: variables.$spacing-xs; margin-right: variables.$spacing-xs; }
.my-1 { margin-top: variables.$spacing-xs; margin-bottom: variables.$spacing-xs; }
.m-2 { margin: variables.$spacing-sm; }
.mt-2 { margin-top: variables.$spacing-sm; }
.mr-2 { margin-right: variables.$spacing-sm; }
.mb-2 { margin-bottom: variables.$spacing-sm; }
.ml-2 { margin-left: variables.$spacing-sm; }
.mx-2 { margin-left: variables.$spacing-sm; margin-right: variables.$spacing-sm; }
.my-2 { margin-top: variables.$spacing-sm; margin-bottom: variables.$spacing-sm; }
.m-3 { margin: variables.$spacing-md; }
.mt-3 { margin-top: variables.$spacing-md; }
.mr-3 { margin-right: variables.$spacing-md; }
.mb-3 { margin-bottom: variables.$spacing-md; }
.ml-3 { margin-left: variables.$spacing-md; }
.mx-3 { margin-left: variables.$spacing-md; margin-right: variables.$spacing-md; }
.my-3 { margin-top: variables.$spacing-md; margin-bottom: variables.$spacing-md; }
.m-4 { margin: variables.$spacing-lg; }
.mt-4 { margin-top: variables.$spacing-lg; }
.mr-4 { margin-right: variables.$spacing-lg; }
.mb-4 { margin-bottom: variables.$spacing-lg; }
.ml-4 { margin-left: variables.$spacing-lg; }
.mx-4 { margin-left: variables.$spacing-lg; margin-right: variables.$spacing-lg; }
.my-4 { margin-top: variables.$spacing-lg; margin-bottom: variables.$spacing-lg; }
.m-5 { margin: variables.$spacing-xl; }
.mt-5 { margin-top: variables.$spacing-xl; }
.mr-5 { margin-right: variables.$spacing-xl; }
.mb-5 { margin-bottom: variables.$spacing-xl; }
.ml-5 { margin-left: variables.$spacing-xl; }
.mx-5 { margin-left: variables.$spacing-xl; margin-right: variables.$spacing-xl; }
.my-5 { margin-top: variables.$spacing-xl; margin-bottom: variables.$spacing-xl; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }
.px-0 { padding-left: 0; padding-right: 0; }
.py-0 { padding-top: 0; padding-bottom: 0; }
.p-1 { padding: variables.$spacing-xs; }
.pt-1 { padding-top: variables.$spacing-xs; }
.pr-1 { padding-right: variables.$spacing-xs; }
.pb-1 { padding-bottom: variables.$spacing-xs; }
.pl-1 { padding-left: variables.$spacing-xs; }
.px-1 { padding-left: variables.$spacing-xs; padding-right: variables.$spacing-xs; }
.py-1 { padding-top: variables.$spacing-xs; padding-bottom: variables.$spacing-xs; }
.p-2 { padding: variables.$spacing-sm; }
.pt-2 { padding-top: variables.$spacing-sm; }
.pr-2 { padding-right: variables.$spacing-sm; }
.pb-2 { padding-bottom: variables.$spacing-sm; }
.pl-2 { padding-left: variables.$spacing-sm; }
.px-2 { padding-left: variables.$spacing-sm; padding-right: variables.$spacing-sm; }
.py-2 { padding-top: variables.$spacing-sm; padding-bottom: variables.$spacing-sm; }
.p-3 { padding: variables.$spacing-md; }
.pt-3 { padding-top: variables.$spacing-md; }
.pr-3 { padding-right: variables.$spacing-md; }
.pb-3 { padding-bottom: variables.$spacing-md; }
.pl-3 { padding-left: variables.$spacing-md; }
.px-3 { padding-left: variables.$spacing-md; padding-right: variables.$spacing-md; }
.py-3 { padding-top: variables.$spacing-md; padding-bottom: variables.$spacing-md; }
.p-4 { padding: variables.$spacing-lg; }
.pt-4 { padding-top: variables.$spacing-lg; }
.pr-4 { padding-right: variables.$spacing-lg; }
.pb-4 { padding-bottom: variables.$spacing-lg; }
.pl-4 { padding-left: variables.$spacing-lg; }
.px-4 { padding-left: variables.$spacing-lg; padding-right: variables.$spacing-lg; }
.py-4 { padding-top: variables.$spacing-lg; padding-bottom: variables.$spacing-lg; }
.p-5 { padding: variables.$spacing-xl; }
.pt-5 { padding-top: variables.$spacing-xl; }
.pr-5 { padding-right: variables.$spacing-xl; }
.pb-5 { padding-bottom: variables.$spacing-xl; }
.pl-5 { padding-left: variables.$spacing-xl; }
.px-5 { padding-left: variables.$spacing-xl; padding-right: variables.$spacing-xl; }
.py-5 { padding-top: variables.$spacing-xl; padding-bottom: variables.$spacing-xl; }

// Text
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-primary { color: variables.$primary-blue; }
.text-link { color: variables.$link; }
.text-black { color: variables.$text-black; }
.text-gray { color: variables.$gray-3; }
.text-white { color: variables.$white; }

// Background
.bg-white { background-color: variables.$white; }
.bg-light { background-color: variables.$light-background; }
.bg-primary { background-color: variables.$primary-blue; }
.bg-primary-light { background-color: variables.$primary-blue-opacity-20; }

// Border
.border { border: variables.$border-width-default solid variables.$gray-2; }
.border-top { border-top: variables.$border-width-default solid variables.$gray-2; }
.border-right { border-right: variables.$border-width-default solid variables.$gray-2; }
.border-bottom { border-bottom: variables.$border-width-default solid variables.$gray-2; }
.border-left { border-left: variables.$border-width-default solid variables.$gray-2; }

// Border Radius
.rounded-sm { border-radius: variables.$border-radius-sm; }
.rounded { border-radius: variables.$border-radius-md; }
.rounded-lg { border-radius: variables.$border-radius-lg; }
.rounded-xl { border-radius: variables.$border-radius-xl; }
.rounded-circle { border-radius: 50%; }

// Width/Height
.w-100 { width: 100%; }
.h-100 { height: 100%; }

// Common Components
.card {
  @include mix.card;
}

// DEPRECATED: Use <app-button> component instead

.input-field {
  @include mix.input-field;
}

.textarea-field {
  @include mix.textarea-field;
}

// DEPRECATED: Use <app-button> component with appropriate variant instead

