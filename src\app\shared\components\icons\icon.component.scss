@use 'variables' as variables;
@use 'mixins' as mix;

.icon {
  display: inline-block;
  vertical-align: middle;
  transition: all 0.2s ease;

  // Size variants
  &.icon-sm {
    width: 16px;
    height: 16px;
  }

  &.icon-md {
    width: 20px;
    height: 20px;
  }

  &.icon-lg {
    width: 24px;
    height: 24px;
  }

  &.icon-xl {
    width: 32px;
    height: 32px;
  }

  // Color variants
  &.icon-primary {
    color: variables.$primary-blue;
  }

  &.icon-secondary {
    color: variables.$gray-3;
  }

  &.icon-success {
    color: variables.$success-green;
  }

  &.icon-danger {
    color: #DC3545;
  }

  &.icon-warning {
    color: #FFC107;
  }

  &.icon-info {
    color: #17A2B8;
  }

  &.icon-light {
    color: variables.$light-primary;
  }

  &.icon-dark {
    color: variables.$text-black;
  }

  // Interactive states
  &.icon-clickable {
    cursor: pointer;

    &:hover {
      transform: scale(1.1);
      opacity: 0.8;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  &.icon-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  // Special styling for specific icons
  &[aria-label*="refresh"] {
    &.icon-clickable:hover {
      transform: rotate(90deg) scale(1.1);
    }
  }

  &[aria-label*="arrow"] {
    &.icon-clickable:hover {
      transform: translateX(2px) scale(1.1);
    }
  }

  &[aria-label*="back"] {
    &.icon-clickable:hover {
      transform: translateX(-2px) scale(1.1);
    }
  }
}

// DEPRECATED: Use ButtonComponent with icon prop instead
// Icon button styling is now handled by ButtonComponent

// Icon in form controls
.form-control .icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;

  &.icon-left {
    left: 12px;
  }

  &.icon-right {
    right: 12px;
  }
}

// Icon in navigation
.navigation-container .icon {
  &.icon-clickable {
    padding: 4px;
    border-radius: 4px;
    
    &:hover {
      background: rgba(variables.$primary-blue, 0.1);
    }
  }
}

// Icon in dropdown
.dropdown-item .icon {
  margin-right: 8px;
  flex-shrink: 0;
}

// Icon in status indicators
.status-indicator .icon {
  margin-right: 6px;
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .icon {
    transition: none;
    
    &.icon-clickable:hover {
      transform: none;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .icon {
    &.icon-light {
      color: variables.$gray-2;
    }
    
    &.icon-secondary {
      color: variables.$text-black;
    }
  }
}
