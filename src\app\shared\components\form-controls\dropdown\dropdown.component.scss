@use 'variables' as variables;
@use 'mixins' as mix;

.dropdown-container {
  position: relative;
  width: 100%;

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.dropdown-label {
  display: block;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: variables.$text-black;
  margin-bottom: variables.$spacing-xs;

  .required-indicator {
    color: variables.$primary-blue;
    margin-left: 2px;
  }
}

.dropdown-trigger {
  padding: 4px; // Exact Figma padding from dropdown.scss line 4
  display: flex; // Exact Figma display from dropdown.scss line 5
  flex-direction: column; // Exact Figma flex-direction from dropdown.scss line 6
  justify-content: center; // Exact Figma justify from dropdown.scss line 7
  align-items: center; // Exact Figma align from dropdown.scss line 9
  gap: 0px; // Exact Figma gap from dropdown.scss line 10
  overflow: hidden; // Exact Figma overflow from dropdown.scss line 12
  border-radius: 10px; // Exact Figma border-radius from dropdown.scss line 13
  border: 1px solid #d9e1e7; // Exact Figma border from dropdown.scss lines 14-16
  background: #ffffff; // Exact Figma background from dropdown.scss line 17
  height: 48px; // Exact Figma height from dropdown.scss line 18
  width: 100%; // Exact Figma width from dropdown.scss line 19
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  box-sizing: border-box;

  &:hover:not(.disabled) {
    border-color: #547996; // gray-3
  }

  &.open {
    border-color: #547996; // gray-3
  }

  &:focus {
    outline: none;
    border-color: #1976d2; // primary-blue
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
  }

  &.disabled {
    background-color: #f1f5f7; // gray-1
    cursor: not-allowed;
  }

  &.has-error {
    border-color: #F4454E;
  }
}

.dropdown-content {
  padding: 8px 12px 8px 12px; // Exact Figma padding from dropdown.scss line 22
  display: flex; // Exact Figma display from dropdown.scss line 23
  flex-direction: row; // Exact Figma flex-direction from dropdown.scss line 24
  justify-content: space-between; // Exact Figma justify from dropdown.scss line 25
  align-items: center; // Exact Figma align from dropdown.scss line 27
  gap: 12px; // Exact Figma gap from dropdown.scss line 28
  border-radius: 6px; // Exact Figma border-radius from dropdown.scss line 30
  background: #ffffff; // Exact Figma background from dropdown.scss line 31
  width: 100%; // Exact Figma width from dropdown.scss line 32
  box-sizing: border-box;
}

.dropdown-text {
  color: #547996; // Exact Figma color from dropdown.scss line 35 (gray-3)
  font-size: 12px; // Exact Figma font-size from dropdown.scss line 36
  font-family: Urbane; // Exact Figma font-family from dropdown.scss line 37
  line-height: 20px; // Exact Figma line-height from dropdown.scss line 38
  font-weight: 300; // Exact Figma font-weight from dropdown.scss line 41
  text-align: left; // Exact Figma text-align from dropdown.scss line 42
  text-wrap: wrap; // Exact Figma text-wrap from dropdown.scss line 43
  width: 100%; // Exact Figma width from dropdown.scss line 44
  flex: 1;

  &.placeholder {
    color: #547996; // gray-3
  }

  &.has-value {
    color: #17181a; // text-black when has value
  }
}

.dropdown-icon {
  position: relative; // Exact Figma position from dropdown.scss line 48
  display: flex; // Exact Figma display from dropdown.scss line 51
  transition: transform 0.2s ease;
  color: #547996; // gray-3
  box-sizing: border-box; // Exact Figma box-sizing from dropdown.scss line 49
  overflow: hidden; // Exact Figma overflow from dropdown.scss line 50

  &.rotated {
    transform: rotate(180deg);
  }

  svg {
    top: 2px; // Exact Figma top from dropdown.scss line 54
    left: 4px; // Exact Figma left from dropdown.scss line 55
    position: absolute; // Exact Figma position from dropdown.scss line 56
    display: flex; // Exact Figma display from dropdown.scss line 57
    width: 14px;
    height: 8px;
  }
}

.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: variables.$white;
  border-radius: 10px;
  border: 1px solid variables.$gray-3;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  margin-top: -1px; // Overlap with trigger border
}

.dropdown-separator {
  height: 1px;
  background: variables.$gray-2;
  margin: 0 8px;
}

.dropdown-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  gap: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 6px;
  margin: 0 4px;

  &:hover:not(.disabled) {
    background-color: variables.$gray-1;
  }

  &.selected {
    background-color: variables.$light-background;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.option-checkbox {
  display: flex;
  align-items: center;

  input[type="checkbox"] {
    @include mix.checkbox;
    margin: 0;
    pointer-events: none; // Prevent direct checkbox interaction
  }
}

.option-text {
  flex: 1;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  line-height: 20px;
  color: variables.$text-black;
}

.error-message {
  margin-top: variables.$spacing-xs;
  font-size: 11px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: #F4454E;
}

// Responsive adjustments
@include mix.for-phone-only {
  .dropdown-options {
    max-height: 200px;
  }
}
