@use 'variables' as variables;
@use 'mixins' as mix;
@use 'sass:color';

.calendar-container {
  position: relative;
  flex: 1 1 0;
  min-width: 0;
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.calendar-label {
  display: block;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: variables.$text-black;
  margin-bottom: variables.$spacing-xs;

  .required-indicator {
    color: variables.$primary-blue;
    margin-left: 2px;
  }
}

.date-input-container {
  // This is the wrapper - keep it functional but apply Figma outer styling
  padding: 4px; // Exact Figma padding from calendar.scss line 4
  overflow: hidden; // Exact Figma overflow from calendar.scss line 12
  border-radius: 10px; // Exact Figma border-radius from calendar.scss line 13
  border: 1px solid #d9e1e7; // Exact Figma border from calendar.scss lines 14-16
  background: #ffffff; // Exact Figma background from calendar.scss line 17
  height: 48px; // Exact Figma height from calendar.scss line 18
  width: 100%; // Exact Figma width from calendar.scss line 19
  flex: 1 1 0;
  min-width: 0;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
  transition: all 0.2s ease;


  // Keep the functional layout for the wrapper
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;

  &:hover:not(.disabled) {
    border-color: #547996; // gray-3
  }

  &.focused {
    border-color: #547996; // gray-3
  }

  &.has-error {
    border-color: #F4454E;
  }

  &.disabled {
    background-color: #f1f5f7; // gray-1
    cursor: not-allowed;
  }
}

.date-input-content {
  padding: 8px 12px 8px 12px; // Exact Figma padding from calendar.scss line 22
  display: flex; // Exact Figma display from calendar.scss line 23
  flex-direction: row; // Exact Figma flex-direction from calendar.scss line 24
  justify-content: flex-start; // Exact Figma justify from calendar.scss line 25
  align-items: center; // Exact Figma align from calendar.scss line 27
  gap: 12px; // Exact Figma gap from calendar.scss line 28
  border-radius: 6px; // Exact Figma border-radius from calendar.scss line 30
  background: #ffffff; // Exact Figma background from calendar.scss line 31
  width: 100%; // Exact Figma width from calendar.scss line 32
  box-sizing: border-box;
}

.date-input {
  flex: 1; // Allow input to take available space
  color: #547996; // Exact Figma color from calendar.scss line 35 (gray-3)
  font-size: 12px; // Exact Figma font-size from calendar.scss line 36
  font-family: Urbane; // Exact Figma font-family from calendar.scss line 37
  line-height: 20px; // Exact Figma line-height from calendar.scss line 38
  font-weight: 300; // Exact Figma font-weight from calendar.scss line 41
  text-align: left; // Exact Figma text-align from calendar.scss line 42
  border: none;
  outline: none;
  background: transparent;

  &::placeholder {
    color: #547996; // gray-3
  }

  &:disabled {
    cursor: not-allowed;
    color: #547996; // gray-3
  }

  &:focus {
    color: #17181a; // text-black when focused
  }

  // When container has value, change text color
  .date-input-container.has-value & {
    color: #17181a; // text-black when has value
  }
}

.calendar-icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  padding: 0;
  transition: color 0.2s ease;
  flex-shrink: 0; // Prevent button from shrinking

  &:hover:not(:disabled) {
    color: #17181a; // text-black
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.calendar-icon {
  width: 16px;
  height: 18px;
  color: #547996; // gray-3
  transition: color 0.2s ease;

  .date-input-container.focused &,
  .date-input-container.has-value & {
    color: #17181a; // text-black
  }
}

.calendar-popup {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: variables.$white;
  border-radius: 10px;
  border: 1px solid variables.$gray-3;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 4px;
  padding: 16px;
  min-width: 280px;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  color: variables.$gray-3;
  transition: all 0.2s ease;

  &:hover {
    background-color: variables.$gray-1;
    color: variables.$text-black;
  }

  svg {
    width: 8px;
    height: 12px;
  }
}

.month-year {
  font-size: 14px;
  font-family: 'Urbane', sans-serif;
  font-weight: 600;
  color: variables.$text-black;
  text-align: center;
  flex: 1;
}

.days-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}

.day-header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  font-size: 11px;
  font-family: 'Urbane', sans-serif;
  font-weight: 600;
  color: variables.$gray-3;
  text-transform: uppercase;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
}

.calendar-day {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: variables.$text-black;
  transition: all 0.2s ease;

  &:hover:not(:disabled):not(.empty) {
    background-color: variables.$gray-1;
  }

  &.selected {
    background-color: variables.$primary-blue;
    color: variables.$white;

    &:hover {
      background-color: color.adjust(variables.$primary-blue, $lightness: -10%);
    }
  }

  &.today {
    font-weight: 600;
    color: variables.$primary-blue;

    &:not(.selected) {
      background-color: rgba(variables.$primary-blue, 0.1);
    }
  }

  &.empty {
    cursor: default;
    visibility: hidden;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.3;
  }
}

.error-message {
  margin-top: variables.$spacing-xs;
  font-size: 11px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: #F4454E;
}

// Responsive adjustments
@include mix.for-phone-only {
  .calendar-popup {
    min-width: 260px;
    padding: 12px;
  }

  .calendar-day {
    width: 28px;
    height: 28px;
    font-size: 11px;
  }

  .day-header {
    height: 28px;
    font-size: 10px;
  }
}
:host {
  display: block;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.calendar-container {
  width: 100%;
  box-sizing: border-box;
}

.date-input-container input {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}