@use 'variables' as variables;
@use 'mixins' as mix;

.checkbox-container {
  display: flex;
  align-items: center;
  gap: variables.$spacing-sm;
}

.checkbox {
  @include mix.checkbox;
  margin: 0;
  cursor: pointer;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.checkbox-label {
  font-size: 12px;
  font-family: 'Urbane', sans-serif;
  font-weight: 300;
  color: variables.$text-black;
  cursor: pointer;
}