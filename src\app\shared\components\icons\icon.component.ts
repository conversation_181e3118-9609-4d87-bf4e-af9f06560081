import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

export type IconName = 
  | 'refresh' 
  | 'back' 
  | 'user' 
  | 'arrow-down' 
  | 'arrow-up' 
  | 'arrow-left' 
  | 'arrow-right'
  | 'check'
  | 'close'
  | 'search'
  | 'filter'
  | 'sort';

export type IconSize = 'sm' | 'md' | 'lg' | 'xl';
export type IconColor = 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark';

@Component({
  selector: 'app-icon',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './icon.component.html',
  styleUrls: ['./icon.component.scss']
})
export class IconComponent {
  @Input() name: IconName = 'refresh';
  @Input() size: IconSize = 'md';
  @Input() color: IconColor = 'primary';
  @Input() customColor?: string;
  @Input() clickable: boolean = false;
  @Input() disabled: boolean = false;

  get iconClasses(): string[] {
    const classes = [
      'icon',
      `icon-${this.size}`,
      `icon-${this.color}`
    ];

    if (this.clickable) {
      classes.push('icon-clickable');
    }

    if (this.disabled) {
      classes.push('icon-disabled');
    }

    return classes;
  }

  get iconStyles(): { [key: string]: string } {
    const styles: { [key: string]: string } = {};
    
    if (this.customColor) {
      styles['color'] = this.customColor;
    }
    
    return styles;
  }

  getSvgPath(): string {
    switch (this.name) {
      case 'refresh':
        return `
          <circle cx="10" cy="10" r="8.33" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <path d="M6.68 13.38h3.6v1.69H6.68z" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <path d="M6.12 9.45h3.6v1.69H6.12z" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <circle cx="7.74" cy="14.31" r="1.11" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <circle cx="12.37" cy="7.9" r="1.11" stroke="currentColor" stroke-width="1.5" fill="none"/>
        `;
      case 'back':
        return `
          <path d="M15.83 10H4.17M9.17 5l-5 5 5 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        `;
      case 'user':
        return `
          <circle cx="10" cy="7.5" r="2.75" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <path d="M4.5 16.5c0-3 2.5-5.5 5.5-5.5s5.5 2.5 5.5 5.5" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <circle cx="10" cy="10" r="8.33" stroke="currentColor" stroke-width="1.5" fill="none"/>
        `;
      case 'arrow-down':
        return `
          <path d="M8 10l4 4 4-4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        `;
      case 'arrow-up':
        return `
          <path d="M16 14l-4-4-4 4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        `;
      case 'arrow-left':
        return `
          <path d="M14 8l-4-4-4 4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" transform="rotate(-90 10 10)"/>
        `;
      case 'arrow-right':
        return `
          <path d="M8 14l4-4-4-4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        `;
      case 'check':
        return `
          <path d="M4 10l4 4 8-8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        `;
      case 'close':
        return `
          <path d="M6 6l8 8M14 6l-8 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        `;
      case 'search':
        return `
          <circle cx="10" cy="10" r="6" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <path d="M16 16l-4-4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        `;
      case 'filter':
        return `
          <path d="M4 6h16M7 12h10M10 18h4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        `;
      case 'sort':
        return `
          <path d="M8 6l4-4 4 4M8 18l4 4 4-4M12 2v20" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        `;
      default:
        return `
          <circle cx="10" cy="10" r="8" stroke="currentColor" stroke-width="1.5" fill="none"/>
          <path d="M10 6v8M6 10h8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        `;
    }
  }
}
