import {
  Component,
  Input,
  Output,
  EventEmitter,
  forwardRef,
  OnChanges,
  SimpleChanges,
  ChangeDetectorRef,
  ChangeDetectionStrategy,
} from '@angular/core';
import { 
  ControlValueAccessor, 
  NG_VALUE_ACCESSOR, 
  NG_VALIDATORS, 
  FormControl, 
  FormGroup, 
  Validators,  
  ReactiveFormsModule,
  AbstractControl,
  ValidationErrors,
   } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-value-input',
  templateUrl: './value-input.component.html',
  styleUrls: ['./value-input.component.scss'],
  standalone: true,
  imports: [ReactiveFormsModule, CommonModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ValueInputComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => ValueInputComponent),
      multi: true,
    }
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ValueInputComponent implements ControlValueAccessor, Validators {
  @Input() label: string = '';
  @Input() placeholder: string = 'Value';
  @Input() formControlName?: string;
 

  value: string = '';
  selectedValue: string = '';
  disabled: boolean = false;

  onChange = (value: any) => {};
  onTouched = () => {};

  constructor(private cdr: ChangeDetectorRef) {}

  writeValue(value: any): void {
    this.value = value;
    this.cdr.markForCheck();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = false; // Ensure the input is always enabled
    this.cdr.markForCheck();
  }

  validate(control: AbstractControl): ValidationErrors | null {
    return null; // or your validation logic
  }

  onInputChange(event: Event): void {
  const input = event.target as HTMLInputElement;
  this.value = input.value;
  this.onChange(this.value);
  this.onTouched(); // Optional: triggers validation
}
}